FROM repo-dev.htsc/public-cncp-image-base-local/node:18 as builder

WORKDIR /app
COPY package.json ./
COPY yarn.lock ./
COPY .husky ./.husky
RUN npm config set registry http://npm.htsc/ 
RUN npm config set disturl http://repo-dev.htsc/artifactory/public-npm-binary-virtual/binaries/node/
RUN npm config list
RUN yarn install

COPY . .
RUN yarn build

FROM repo-dev.htsc/public-cncp-image-base-local/node:18 as deploy
WORKDIR /app
COPY --from=builder /app/static ./static
COPY --from=builder /app/views ./views
COPY --from=builder /app/start.sh ./start.sh
COPY --from=builder /app/package.json ./
COPY --from=builder /app/yarn.lock ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist

RUN chmod +x /app/start.sh

EXPOSE 3000
ENTRYPOINT sh /app/start.sh ${env}