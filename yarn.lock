# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.1.0":
  version "2.2.0"
  resolved "http://npm.htsc/@ampproject/remapping/download/@ampproject/remapping-2.2.0.tgz#56c133824780de3174aed5ab6834f3026790154d"
  integrity sha1-VsEzgkeA3jF0rtWraDTzAmeQFU0=
  dependencies:
    "@jridgewell/gen-mapping" "^0.1.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@angular-devkit/core@14.0.5":
  version "14.0.5"
  resolved "http://npm.htsc/@angular-devkit/core/download/@angular-devkit/core-14.0.5.tgz#19f5940b53aeb0ce56479c44670d3bc3b2df92b1"
  integrity sha1-GfWUC1OusM5WR5xEZw07w7LfkrE=
  dependencies:
    ajv "8.11.0"
    ajv-formats "2.1.1"
    jsonc-parser "3.0.0"
    rxjs "6.6.7"
    source-map "0.7.3"

"@angular-devkit/core@17.3.6":
  version "17.3.6"
  resolved "http://registry.npm.htsc/@angular-devkit/core/-/core-17.3.6.tgz#ce2eedff64ae4c50aed5222b1b7f00b8648e9c42"
  integrity sha512-FVbkT9dEwHEvjnxr4mvMNSMg2bCFoGoP4X68xXU9dhLEUpC05opLvfbaR3Qh543eCJ5AstosBFVzB/krfIkOvA==
  dependencies:
    ajv "8.12.0"
    ajv-formats "2.1.1"
    jsonc-parser "3.2.1"
    picomatch "4.0.1"
    rxjs "7.8.1"
    source-map "0.7.4"

"@angular-devkit/schematics-cli@14.0.5":
  version "14.0.5"
  resolved "http://npm.htsc/@angular-devkit/schematics-cli/download/@angular-devkit/schematics-cli-14.0.5.tgz#a1ec438cca8814650bf7ecbcbb7c182e69437669"
  integrity sha1-oexDjMqIFGUL9+y8u3wYLmlDdmk=
  dependencies:
    "@angular-devkit/core" "14.0.5"
    "@angular-devkit/schematics" "14.0.5"
    ansi-colors "4.1.1"
    inquirer "8.2.4"
    symbol-observable "4.0.0"
    yargs-parser "21.0.1"

"@angular-devkit/schematics-cli@^17.3.6":
  version "17.3.6"
  resolved "http://registry.npm.htsc/@angular-devkit/schematics-cli/-/schematics-cli-17.3.6.tgz#91b893c7dcaaa58f85f6063b82b3195757c5b028"
  integrity sha512-PeUQm6453515ZbBjE8r7FjYJHCRRh4Abvi7xl0JMGPeyk//E7ogblCdUJ1uYlafl9KvD8bRBe2SaqMRfIdlKdQ==
  dependencies:
    "@angular-devkit/core" "17.3.6"
    "@angular-devkit/schematics" "17.3.6"
    ansi-colors "4.1.3"
    inquirer "9.2.15"
    symbol-observable "4.0.0"
    yargs-parser "21.1.1"

"@angular-devkit/schematics@14.0.5":
  version "14.0.5"
  resolved "http://npm.htsc/@angular-devkit/schematics/download/@angular-devkit/schematics-14.0.5.tgz#01777d2ad473d35bdfdbbb751521c43421ad9772"
  integrity sha1-AXd9KtRz01vf27t1FSHENCGtl3I=
  dependencies:
    "@angular-devkit/core" "14.0.5"
    jsonc-parser "3.0.0"
    magic-string "0.26.1"
    ora "5.4.1"
    rxjs "6.6.7"

"@angular-devkit/schematics@17.3.6":
  version "17.3.6"
  resolved "http://registry.npm.htsc/@angular-devkit/schematics/-/schematics-17.3.6.tgz#687ed3ea1bbb201708696ede3912ea9551ca5d26"
  integrity sha512-2G1YuPInd8znG7uUgKOS7z72Aku50lTzB/2csWkWPJLAFkh7vKC8QZ40x8S1nC9npVYPhI5CRLX/HVpBh9CyxA==
  dependencies:
    "@angular-devkit/core" "17.3.6"
    jsonc-parser "3.2.1"
    magic-string "0.30.8"
    ora "5.4.1"
    rxjs "7.8.1"

"@apollo/cache-control-types@^1.0.2":
  version "1.0.2"
  resolved "http://npm.htsc/@apollo/cache-control-types/download/@apollo/cache-control-types-1.0.2.tgz#f42ed3563acc7f1f50617d65d208483977adc68e"
  integrity sha1-9C7TVjrMfx9QYX1l0ghIOXetxo4=

"@apollo/protobufjs@1.2.7":
  version "1.2.7"
  resolved "http://npm.htsc/@apollo/protobufjs/download/@apollo/protobufjs-1.2.7.tgz#3a8675512817e4a046a897e5f4f16415f16a7d8a"
  integrity sha1-OoZ1USgX5KBGqJfl9PFkFfFqfYo=
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/long" "^4.0.0"
    long "^4.0.0"

"@apollo/server-gateway-interface@^1.1.0":
  version "1.1.0"
  resolved "http://npm.htsc/@apollo/server-gateway-interface/download/@apollo/server-gateway-interface-1.1.0.tgz#592a6dfcf0359a15785ec62c1b6fa51ca761fe08"
  integrity sha1-WSpt/PA1mhV4XsYsG2+lHKdh/gg=
  dependencies:
    "@apollo/usage-reporting-protobuf" "^4.0.0"
    "@apollo/utils.fetcher" "^2.0.0"
    "@apollo/utils.keyvaluecache" "^2.1.0"
    "@apollo/utils.logger" "^2.0.0"

"@apollo/server-plugin-landing-page-graphql-playground@4.0.0":
  version "4.0.0"
  resolved "http://npm.htsc/@apollo/server-plugin-landing-page-graphql-playground/download/@apollo/server-plugin-landing-page-graphql-playground-4.0.0.tgz#eff593de6c37a0b63d740f1c6498d69f67644aed"
  integrity sha1-7/WT3mw3oLY9dA8cZJjWn2dkSu0=
  dependencies:
    "@apollographql/graphql-playground-html" "1.6.29"

"@apollo/server@^4.7.3":
  version "4.7.3"
  resolved "http://npm.htsc/@apollo/server/download/@apollo/server-4.7.3.tgz#5b1dcc5d5b2da80580d2f3c1914f2110881f431b"
  integrity sha1-Wx3MXVstqAWA0vPBkU8hEIgfQxs=
  dependencies:
    "@apollo/cache-control-types" "^1.0.2"
    "@apollo/server-gateway-interface" "^1.1.0"
    "@apollo/usage-reporting-protobuf" "^4.1.0"
    "@apollo/utils.createhash" "^2.0.0"
    "@apollo/utils.fetcher" "^2.0.0"
    "@apollo/utils.isnodelike" "^2.0.0"
    "@apollo/utils.keyvaluecache" "^2.1.0"
    "@apollo/utils.logger" "^2.0.0"
    "@apollo/utils.usagereporting" "^2.1.0"
    "@apollo/utils.withrequired" "^2.0.0"
    "@graphql-tools/schema" "^9.0.0"
    "@josephg/resolvable" "^1.0.0"
    "@types/express" "^4.17.13"
    "@types/express-serve-static-core" "^4.17.30"
    "@types/node-fetch" "^2.6.1"
    async-retry "^1.2.1"
    body-parser "^1.20.0"
    cors "^2.8.5"
    express "^4.17.1"
    loglevel "^1.6.8"
    lru-cache "^7.10.1"
    negotiator "^0.6.3"
    node-abort-controller "^3.1.1"
    node-fetch "^2.6.7"
    uuid "^9.0.0"
    whatwg-mimetype "^3.0.0"

"@apollo/usage-reporting-protobuf@^4.0.0", "@apollo/usage-reporting-protobuf@^4.1.0":
  version "4.1.0"
  resolved "http://npm.htsc/@apollo/usage-reporting-protobuf/download/@apollo/usage-reporting-protobuf-4.1.0.tgz#b54b8c32702bbe81aa0e399076ddabaf75a13f9b"
  integrity sha1-tUuMMnArvoGqDjmQdt2rr3WhP5s=
  dependencies:
    "@apollo/protobufjs" "1.2.7"

"@apollo/utils.createhash@^2.0.0":
  version "2.0.1"
  resolved "http://npm.htsc/@apollo/utils.createhash/download/@apollo/utils.createhash-2.0.1.tgz#9d982a166833ce08265ff70f8ef781d65109bdaa"
  integrity sha1-nZgqFmgzzggmX/cPjveB1lEJvao=
  dependencies:
    "@apollo/utils.isnodelike" "^2.0.1"
    sha.js "^2.4.11"

"@apollo/utils.dropunuseddefinitions@^2.0.1":
  version "2.0.1"
  resolved "http://npm.htsc/@apollo/utils.dropunuseddefinitions/download/@apollo/utils.dropunuseddefinitions-2.0.1.tgz#916cd912cbd88769d3b0eab2d24f4674eeda8124"
  integrity sha1-kWzZEsvYh2nTsOqy0k9GdO7agSQ=

"@apollo/utils.fetcher@^2.0.0":
  version "2.0.1"
  resolved "http://npm.htsc/@apollo/utils.fetcher/download/@apollo/utils.fetcher-2.0.1.tgz#2f6e3edc8ce79fbe916110d9baaddad7e13d955f"
  integrity sha1-L24+3Iznn76RYRDZuq3a1+E9lV8=

"@apollo/utils.isnodelike@^2.0.0", "@apollo/utils.isnodelike@^2.0.1":
  version "2.0.1"
  resolved "http://npm.htsc/@apollo/utils.isnodelike/download/@apollo/utils.isnodelike-2.0.1.tgz#08a7e50f08d2031122efa25af089d1c6ee609f31"
  integrity sha1-CKflDwjSAxEi76Ja8InRxu5gnzE=

"@apollo/utils.keyvaluecache@^2.1.0":
  version "2.1.1"
  resolved "http://npm.htsc/@apollo/utils.keyvaluecache/download/@apollo/utils.keyvaluecache-2.1.1.tgz#f3f79a2f00520c6ab7a77a680a4e1fec4d19e1a6"
  integrity sha1-8/eaLwBSDGq3p3poCk4f7E0Z4aY=
  dependencies:
    "@apollo/utils.logger" "^2.0.1"
    lru-cache "^7.14.1"

"@apollo/utils.logger@^2.0.0", "@apollo/utils.logger@^2.0.1":
  version "2.0.1"
  resolved "http://npm.htsc/@apollo/utils.logger/download/@apollo/utils.logger-2.0.1.tgz#74faeb97d7ad9f22282dfb465bcb2e6873b8a625"
  integrity sha1-dPrrl9etnyIoLftGW8suaHO4piU=

"@apollo/utils.printwithreducedwhitespace@^2.0.1":
  version "2.0.1"
  resolved "http://npm.htsc/@apollo/utils.printwithreducedwhitespace/download/@apollo/utils.printwithreducedwhitespace-2.0.1.tgz#f4fadea0ae849af2c19c339cc5420d1ddfaa905e"
  integrity sha1-9PreoK6EmvLBnDOcxUINHd+qkF4=

"@apollo/utils.removealiases@2.0.1":
  version "2.0.1"
  resolved "http://npm.htsc/@apollo/utils.removealiases/download/@apollo/utils.removealiases-2.0.1.tgz#2873c93d72d086c60fc0d77e23d0f75e66a2598f"
  integrity sha1-KHPJPXLQhsYPwNd+I9D3XmaiWY8=

"@apollo/utils.sortast@^2.0.1":
  version "2.0.1"
  resolved "http://npm.htsc/@apollo/utils.sortast/download/@apollo/utils.sortast-2.0.1.tgz#58c90bb8bd24726346b61fa51ba7fcf06e922ef7"
  integrity sha1-WMkLuL0kcmNGth+lG6f88G6SLvc=
  dependencies:
    lodash.sortby "^4.7.0"

"@apollo/utils.stripsensitiveliterals@^2.0.1":
  version "2.0.1"
  resolved "http://npm.htsc/@apollo/utils.stripsensitiveliterals/download/@apollo/utils.stripsensitiveliterals-2.0.1.tgz#2f3350483be376a98229f90185eaf19888323132"
  integrity sha1-LzNQSDvjdqmCKfkBherxmIgyMTI=

"@apollo/utils.usagereporting@^2.1.0":
  version "2.1.0"
  resolved "http://npm.htsc/@apollo/utils.usagereporting/download/@apollo/utils.usagereporting-2.1.0.tgz#11bca6a61fcbc6e6d812004503b38916e74313f4"
  integrity sha1-Ebymph/LxubYEgBFA7OJFudDE/Q=
  dependencies:
    "@apollo/usage-reporting-protobuf" "^4.1.0"
    "@apollo/utils.dropunuseddefinitions" "^2.0.1"
    "@apollo/utils.printwithreducedwhitespace" "^2.0.1"
    "@apollo/utils.removealiases" "2.0.1"
    "@apollo/utils.sortast" "^2.0.1"
    "@apollo/utils.stripsensitiveliterals" "^2.0.1"

"@apollo/utils.withrequired@^2.0.0":
  version "2.0.1"
  resolved "http://npm.htsc/@apollo/utils.withrequired/download/@apollo/utils.withrequired-2.0.1.tgz#e72bc512582a6f26af150439f7eb7473b46ba874"
  integrity sha1-5yvFElgqbyavFQQ59+t0c7RrqHQ=

"@apollographql/graphql-playground-html@1.6.29":
  version "1.6.29"
  resolved "http://npm.htsc/@apollographql/graphql-playground-html/download/@apollographql/graphql-playground-html-1.6.29.tgz#a7a646614a255f62e10dcf64a7f68ead41dec453"
  integrity sha1-p6ZGYUolX2LhDc9kp/aOrUHexFM=
  dependencies:
    xss "^1.0.8"

"@babel/code-frame@7.12.11":
  version "7.12.11"
  resolved "http://npm.htsc/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz#f4ad435aa263db935b8f10f2c552d23fb716a63f"
  integrity sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.16.7", "@babel/code-frame@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/code-frame/download/@babel/code-frame-7.18.6.tgz#3b25d38c89600baa2dcc219edfa88a74eb2c427a"
  integrity sha1-OyXTjIlgC6otzCGe36iKdOssQno=
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/compat-data@^7.17.7", "@babel/compat-data@^7.19.3", "@babel/compat-data@^7.19.4":
  version "7.19.4"
  resolved "http://npm.htsc/@babel/compat-data/download/@babel/compat-data-7.19.4.tgz#95c86de137bf0317f3a570e1b6e996b427299747"
  integrity sha1-lcht4Te/AxfzpXDhtumWtCcpl0c=

"@babel/core@^7.11.6", "@babel/core@^7.12.3", "@babel/core@^7.18.0":
  version "7.19.6"
  resolved "http://npm.htsc/@babel/core/download/@babel/core-7.19.6.tgz#7122ae4f5c5a37c0946c066149abd8e75f81540f"
  integrity sha1-cSKuT1xaN8CUbAZhSavY51+BVA8=
  dependencies:
    "@ampproject/remapping" "^2.1.0"
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.19.6"
    "@babel/helper-compilation-targets" "^7.19.3"
    "@babel/helper-module-transforms" "^7.19.6"
    "@babel/helpers" "^7.19.4"
    "@babel/parser" "^7.19.6"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.19.6"
    "@babel/types" "^7.19.4"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.1"
    semver "^6.3.0"

"@babel/eslint-parser@^7.18.9":
  version "7.19.1"
  resolved "http://npm.htsc/@babel/eslint-parser/download/@babel/eslint-parser-7.19.1.tgz#4f68f6b0825489e00a24b41b6a1ae35414ecd2f4"
  integrity sha1-T2j2sIJUieAKJLQbahrjVBTs0vQ=
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
    eslint-visitor-keys "^2.1.0"
    semver "^6.3.0"

"@babel/eslint-plugin@^7.13.10":
  version "7.19.1"
  resolved "http://npm.htsc/@babel/eslint-plugin/download/@babel/eslint-plugin-7.19.1.tgz#8bfde4b6e4380ea038e7947a765fe536c3057a4c"
  integrity sha1-i/3ktuQ4DqA455R6dl/lNsMFekw=
  dependencies:
    eslint-rule-composer "^0.3.0"

"@babel/generator@^7.19.6", "@babel/generator@^7.7.2":
  version "7.19.6"
  resolved "http://npm.htsc/@babel/generator/download/@babel/generator-7.19.6.tgz#9e481a3fe9ca6261c972645ae3904ec0f9b34a1d"
  integrity sha1-nkgaP+nKYmHJcmRa45BOwPmzSh0=
  dependencies:
    "@babel/types" "^7.19.4"
    "@jridgewell/gen-mapping" "^0.3.2"
    jsesc "^2.5.1"

"@babel/generator@^7.20.0":
  version "7.20.0"
  resolved "http://npm.htsc/@babel/generator/download/@babel/generator-7.20.0.tgz#0bfc5379e0efb05ca6092091261fcdf7ec36249d"
  integrity sha1-C/xTeeDvsFymCSCRJh/N9+w2JJ0=
  dependencies:
    "@babel/types" "^7.20.0"
    "@jridgewell/gen-mapping" "^0.3.2"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.18.6.tgz#eaa49f6f80d5a33f9a5dd2276e6d6e451be0a6bb"
  integrity sha1-6qSfb4DVoz+aXdInbm1uRRvgprs=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.18.6":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.18.9.tgz#acd4edfd7a566d1d51ea975dff38fd52906981bb"
  integrity sha1-rNTt/XpWbR1R6pdd/zj9UpBpgbs=
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.18.6"
    "@babel/types" "^7.18.9"

"@babel/helper-compilation-targets@^7.17.7", "@babel/helper-compilation-targets@^7.18.9", "@babel/helper-compilation-targets@^7.19.0", "@babel/helper-compilation-targets@^7.19.3":
  version "7.19.3"
  resolved "http://npm.htsc/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.19.3.tgz#a10a04588125675d7c7ae299af86fa1b2ee038ca"
  integrity sha1-oQoEWIElZ118euKZr4b6Gy7gOMo=
  dependencies:
    "@babel/compat-data" "^7.19.3"
    "@babel/helper-validator-option" "^7.18.6"
    browserslist "^4.21.3"
    semver "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.19.0":
  version "7.19.0"
  resolved "http://npm.htsc/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.19.0.tgz#bfd6904620df4e46470bae4850d66be1054c404b"
  integrity sha1-v9aQRiDfTkZHC65IUNZr4QVMQEs=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-member-expression-to-functions" "^7.18.9"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-replace-supers" "^7.18.9"
    "@babel/helper-split-export-declaration" "^7.18.6"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.19.0":
  version "7.19.0"
  resolved "http://npm.htsc/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.19.0.tgz#7976aca61c0984202baca73d84e2337a5424a41b"
  integrity sha1-eXasphwJhCArrKc9hOIzelQkpBs=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    regexpu-core "^5.1.0"

"@babel/helper-define-polyfill-provider@^0.3.3":
  version "0.3.3"
  resolved "http://npm.htsc/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.3.3.tgz#8612e55be5d51f0cd1f36b4a5a83924e89884b7a"
  integrity sha1-hhLlW+XVHwzR82tKWoOSTomIS3o=
  dependencies:
    "@babel/helper-compilation-targets" "^7.17.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"
    semver "^6.1.2"

"@babel/helper-environment-visitor@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/helper-environment-visitor/download/@babel/helper-environment-visitor-7.18.9.tgz#0c0cee9b35d2ca190478756865bb3528422f51be"
  integrity sha1-DAzumzXSyhkEeHVoZbs1KEIvUb4=

"@babel/helper-explode-assignable-expression@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.18.6.tgz#41f8228ef0a6f1a036b8dfdfec7ce94f9a6bc096"
  integrity sha1-QfgijvCm8aA2uN/f7HzpT5prwJY=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-function-name@^7.18.9", "@babel/helper-function-name@^7.19.0":
  version "7.19.0"
  resolved "http://npm.htsc/@babel/helper-function-name/download/@babel/helper-function-name-7.19.0.tgz#941574ed5390682e872e52d3f38ce9d1bef4648c"
  integrity sha1-lBV07VOQaC6HLlLT84zp0b70ZIw=
  dependencies:
    "@babel/template" "^7.18.10"
    "@babel/types" "^7.19.0"

"@babel/helper-hoist-variables@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.18.6.tgz#d4d2c8fb4baeaa5c68b99cc8245c56554f926678"
  integrity sha1-1NLI+0uuqlxouZzIJFxWVU+SZng=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-member-expression-to-functions@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.18.9.tgz#1531661e8375af843ad37ac692c132841e2fd815"
  integrity sha1-FTFmHoN1r4Q603rGksEyhB4v2BU=
  dependencies:
    "@babel/types" "^7.18.9"

"@babel/helper-module-imports@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/helper-module-imports/download/@babel/helper-module-imports-7.18.6.tgz#1e3ebdbbd08aad1437b428c50204db13c5a3ca6e"
  integrity sha1-Hj69u9CKrRQ3tCjFAgTbE8Wjym4=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-module-transforms@^7.18.6", "@babel/helper-module-transforms@^7.19.0", "@babel/helper-module-transforms@^7.19.6":
  version "7.19.6"
  resolved "http://npm.htsc/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.19.6.tgz#6c52cc3ac63b70952d33ee987cbee1c9368b533f"
  integrity sha1-bFLMOsY7cJUtM+6YfL7hyTaLUz8=
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-simple-access" "^7.19.4"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/helper-validator-identifier" "^7.19.1"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.19.6"
    "@babel/types" "^7.19.4"

"@babel/helper-optimise-call-expression@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.18.6.tgz#9369aa943ee7da47edab2cb4e838acf09d290ffe"
  integrity sha1-k2mqlD7n2kftqyy06Dis8J0pD/4=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.18.9", "@babel/helper-plugin-utils@^7.19.0", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.19.0"
  resolved "http://npm.htsc/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.19.0.tgz#4796bb14961521f0f8715990bee2fb6e51ce21bf"
  integrity sha1-R5a7FJYVIfD4cVmQvuL7blHOIb8=

"@babel/helper-remap-async-to-generator@^7.18.6", "@babel/helper-remap-async-to-generator@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.18.9.tgz#997458a0e3357080e54e1d79ec347f8a8cd28519"
  integrity sha1-mXRYoOM1cIDlTh157DR/iozShRk=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-wrap-function" "^7.18.9"
    "@babel/types" "^7.18.9"

"@babel/helper-replace-supers@^7.18.6", "@babel/helper-replace-supers@^7.18.9":
  version "7.19.1"
  resolved "http://npm.htsc/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.19.1.tgz#e1592a9b4b368aa6bdb8784a711e0bcbf0612b78"
  integrity sha1-4Vkqm0s2iqa9uHhKcR4Ly/BhK3g=
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-member-expression-to-functions" "^7.18.9"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/traverse" "^7.19.1"
    "@babel/types" "^7.19.0"

"@babel/helper-simple-access@^7.19.4":
  version "7.19.4"
  resolved "http://npm.htsc/@babel/helper-simple-access/download/@babel/helper-simple-access-7.19.4.tgz#be553f4951ac6352df2567f7daa19a0ee15668e7"
  integrity sha1-vlU/SVGsY1LfJWf32qGaDuFWaOc=
  dependencies:
    "@babel/types" "^7.19.4"

"@babel/helper-skip-transparent-expression-wrappers@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.18.9.tgz#778d87b3a758d90b471e7b9918f34a9a02eb5818"
  integrity sha1-d42Hs6dY2QtHHnuZGPNKmgLrWBg=
  dependencies:
    "@babel/types" "^7.18.9"

"@babel/helper-split-export-declaration@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.18.6.tgz#7367949bc75b20c6d5a5d4a97bba2824ae8ef075"
  integrity sha1-c2eUm8dbIMbVpdSpe7ooJK6O8HU=
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-string-parser@^7.19.4":
  version "7.19.4"
  resolved "http://npm.htsc/@babel/helper-string-parser/download/@babel/helper-string-parser-7.19.4.tgz#38d3acb654b4701a9b77fb0615a96f775c3a9e63"
  integrity sha1-ONOstlS0cBqbd/sGFalvd1w6nmM=

"@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.19.1":
  version "7.19.1"
  resolved "http://npm.htsc/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.19.1.tgz#7eea834cf32901ffdc1a7ee555e2f9c27e249ca2"
  integrity sha1-fuqDTPMpAf/cGn7lVeL5wn4knKI=

"@babel/helper-validator-option@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/helper-validator-option/download/@babel/helper-validator-option-7.18.6.tgz#bf0d2b5a509b1f336099e4ff36e1a63aa5db4db8"
  integrity sha1-vw0rWlCbHzNgmeT/NuGmOqXbTbg=

"@babel/helper-wrap-function@^7.18.9":
  version "7.18.11"
  resolved "http://npm.htsc/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.18.11.tgz#bff23ace436e3f6aefb61f85ffae2291c80ed1fb"
  integrity sha1-v/I6zkNuP2rvth+F/64ikcgO0fs=
  dependencies:
    "@babel/helper-function-name" "^7.18.9"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.18.11"
    "@babel/types" "^7.18.10"

"@babel/helpers@^7.19.4":
  version "7.19.4"
  resolved "http://npm.htsc/@babel/helpers/download/@babel/helpers-7.19.4.tgz#42154945f87b8148df7203a25c31ba9a73be46c5"
  integrity sha1-QhVJRfh7gUjfcgOiXDG6mnO+RsU=
  dependencies:
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.19.4"
    "@babel/types" "^7.19.4"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/highlight/download/@babel/highlight-7.18.6.tgz#81158601e93e2563795adcbfbdf5d64be3f2ecdf"
  integrity sha1-gRWGAek+JWN5Wty/vfXWS+Py7N8=
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.18.10", "@babel/parser@^7.19.6":
  version "7.19.6"
  resolved "http://npm.htsc/@babel/parser/download/@babel/parser-7.19.6.tgz#b923430cb94f58a7eae8facbffa9efd19130e7f8"
  integrity sha1-uSNDDLlPWKfq6PrL/6nv0ZEw5/g=

"@babel/parser@^7.20.0":
  version "7.20.0"
  resolved "http://npm.htsc/@babel/parser/download/@babel/parser-7.20.0.tgz#b26133c888da4d79b0d3edcf42677bcadc783046"
  integrity sha1-smEzyIjaTXmw0+3PQmd7ytx4MEY=

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/download/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.18.6.tgz#da5b8f9a580acdfbe53494dba45ea389fb09a4d2"
  integrity sha1-2luPmlgKzfvlNJTbpF6jifsJpNI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.18.9.tgz#a11af19aa373d68d561f08e0a57242350ed0ec50"
  integrity sha1-oRrxmqNz1o1WHwjgpXJCNQ7Q7FA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"
    "@babel/plugin-proposal-optional-chaining" "^7.18.9"

"@babel/plugin-proposal-async-generator-functions@^7.19.1":
  version "7.19.1"
  resolved "http://npm.htsc/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.19.1.tgz#34f6f5174b688529342288cd264f80c9ea9fb4a7"
  integrity sha1-NPb1F0tohSk0IojNJk+AyeqftKc=
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-remap-async-to-generator" "^7.18.9"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.18.6.tgz#b110f59741895f7ec21a6fff696ec46265c446a3"
  integrity sha1-sRD1l0GJX37CGm//aW7EYmXERqM=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-class-static-block@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-proposal-class-static-block/download/@babel/plugin-proposal-class-static-block-7.18.6.tgz#8aa81d403ab72d3962fc06c26e222dacfc9b9020"
  integrity sha1-iqgdQDq3LTli/AbCbiItrPybkCA=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-dynamic-import@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-proposal-dynamic-import/download/@babel/plugin-proposal-dynamic-import-7.18.6.tgz#72bcf8d408799f547d759298c3c27c7e7faa4d94"
  integrity sha1-crz41Ah5n1R9dZKYw8J8fn+qTZQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/plugin-proposal-export-namespace-from/download/@babel/plugin-proposal-export-namespace-from-7.18.9.tgz#5f7313ab348cdb19d590145f9247540e94761203"
  integrity sha1-X3MTqzSM2xnVkBRfkkdUDpR2EgM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.18.6.tgz#7e8788c1811c393aff762817e7dbf1ebd0c05f0b"
  integrity sha1-foeIwYEcOTr/digX59vx69DAXws=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/plugin-proposal-logical-assignment-operators/download/@babel/plugin-proposal-logical-assignment-operators-7.18.9.tgz#8148cbb350483bf6220af06fa6db3690e14b2e23"
  integrity sha1-gUjLs1BIO/YiCvBvpts2kOFLLiM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz#fdd940a99a740e577d6c753ab6fbb43fdb9467e1"
  integrity sha1-/dlAqZp0Dld9bHU6tvu0P9uUZ+E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-proposal-numeric-separator/download/@babel/plugin-proposal-numeric-separator-7.18.6.tgz#899b14fbafe87f053d2c5ff05b36029c62e13c75"
  integrity sha1-iZsU+6/ofwU9LF/wWzYCnGLhPHU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.19.4":
  version "7.19.4"
  resolved "http://npm.htsc/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.19.4.tgz#a8fc86e8180ff57290c91a75d83fe658189b642d"
  integrity sha1-qPyG6BgP9XKQyRp12D/mWBibZC0=
  dependencies:
    "@babel/compat-data" "^7.19.4"
    "@babel/helper-compilation-targets" "^7.19.3"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.18.8"

"@babel/plugin-proposal-optional-catch-binding@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.18.6.tgz#f9400d0e6a3ea93ba9ef70b09e72dd6da638a2cb"
  integrity sha1-+UANDmo+qTup73CwnnLdbaY4oss=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.18.9.tgz#e8e8fe0723f2563960e4bf5e9690933691915993"
  integrity sha1-6Oj+ByPyVjlg5L9elpCTNpGRWZM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-proposal-private-methods/download/@babel/plugin-proposal-private-methods-7.18.6.tgz#5209de7d213457548a98436fa2882f52f4be6bea"
  integrity sha1-UgnefSE0V1SKmENvoogvUvS+a+o=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-private-property-in-object@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.18.6.tgz#a64137b232f0aca3733a67eb1a144c192389c503"
  integrity sha1-pkE3sjLwrKNzOmfrGhRMGSOJxQM=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.18.6", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.18.6.tgz#af613d2cd5e643643b65cded64207b15c85cb78e"
  integrity sha1-r2E9LNXmQ2Q7Zc3tZCB7Fchct44=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "http://npm.htsc/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz#a983fb1aeb2ec3f6ed042a210f640e90e786fe0d"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz#4c9a6f669f5d0cdf1b90a1671e9a146be5300cea"
  integrity sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13", "@babel/plugin-syntax-class-properties@^7.8.3":
  version "7.12.13"
  resolved "http://npm.htsc/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz#b5c987274c4a3a82b89714796931a6b53544ae10"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "http://npm.htsc/@babel/plugin-syntax-class-static-block/download/@babel/plugin-syntax-class-static-block-7.14.5.tgz#195df89b146b4b78b3bf897fd7a257c84659d406"
  integrity sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz#62bf98b2da3cd21d626154fc96ee5b3cb68eacb3"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-export-namespace-from/download/@babel/plugin-syntax-export-namespace-from-7.8.3.tgz#028964a9ba80dbc094c915c487ad7c4e7a66465a"
  integrity sha1-AolkqbqA28CUyRXEh618TnpmRlo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-import-assertions@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-syntax-import-assertions/download/@babel/plugin-syntax-import-assertions-7.18.6.tgz#cd6190500a4fa2fe31990a963ffab4b63e4505e4"
  integrity sha1-zWGQUApPov4xmQqWP/q0tj5FBeQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-import-meta@^7.8.3":
  version "7.10.4"
  resolved "http://npm.htsc/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz#ee601348c370fa334d2207be158777496521fd51"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz#01ca21b668cd8218c9e640cb6dd88c5412b2c96a"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.18.6.tgz#a8feef63b010150abd97f1649ec296e849943ca0"
  integrity sha1-qP7vY7AQFQq9l/FknsKW6EmUPKA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4", "@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  version "7.10.4"
  resolved "http://npm.htsc/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz#ca91ef46303530448b906652bac2e9fe9941f699"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz#167ed70368886081f74b5c36c65a88c03b66d1a9"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4", "@babel/plugin-syntax-numeric-separator@^7.8.3":
  version "7.10.4"
  resolved "http://npm.htsc/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz#b9b070b3e33570cd9fd07ba7fa91c0dd37b9af97"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "http://npm.htsc/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "http://npm.htsc/@babel/plugin-syntax-private-property-in-object/download/@babel/plugin-syntax-private-property-in-object-7.14.5.tgz#0dc6671ec0ea22b6e94a1114f857970cd39de1ad"
  integrity sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5", "@babel/plugin-syntax-top-level-await@^7.8.3":
  version "7.14.5"
  resolved "http://npm.htsc/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz#c1cfdadc35a646240001f06138247b741c34d94c"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.18.6", "@babel/plugin-syntax-typescript@^7.7.2":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.18.6.tgz#1c09cd25795c7c2b8a4ba9ae49394576d4133285"
  integrity sha1-HAnNJXlcfCuKS6muSTlFdtQTMoU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.18.6.tgz#19063fcf8771ec7b31d742339dac62433d0611fe"
  integrity sha1-GQY/z4dx7Hsx10IznaxiQz0GEf4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-async-to-generator@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.18.6.tgz#ccda3d1ab9d5ced5265fdb13f1882d5476c71615"
  integrity sha1-zNo9GrnVztUmX9sT8YgtVHbHFhU=
  dependencies:
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-remap-async-to-generator" "^7.18.6"

"@babel/plugin-transform-block-scoped-functions@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.18.6.tgz#9187bf4ba302635b9d70d986ad70f038726216a8"
  integrity sha1-kYe/S6MCY1udcNmGrXDwOHJiFqg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-block-scoping@^7.19.4":
  version "7.19.4"
  resolved "http://npm.htsc/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.19.4.tgz#315d70f68ce64426db379a3d830e7ac30be02e9b"
  integrity sha1-MV1w9ozmRCbbN5o9gw56wwvgLps=
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-transform-classes@^7.19.0":
  version "7.19.0"
  resolved "http://npm.htsc/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.19.0.tgz#0e61ec257fba409c41372175e7c1e606dc79bb20"
  integrity sha1-DmHsJX+6QJxBNyF158HmBtx5uyA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-compilation-targets" "^7.19.0"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-replace-supers" "^7.18.9"
    "@babel/helper-split-export-declaration" "^7.18.6"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.18.9.tgz#2357a8224d402dad623caf6259b611e56aec746e"
  integrity sha1-I1eoIk1ALa1iPK9iWbYR5WrsdG4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-destructuring@^7.18.0", "@babel/plugin-transform-destructuring@^7.19.4":
  version "7.19.4"
  resolved "http://npm.htsc/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.19.4.tgz#46890722687b9b89e1369ad0bd8dc6c5a3b4319d"
  integrity sha1-RokHImh7m4nhNprQvY3GxaO0MZ0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-transform-dotall-regex@^7.18.6", "@babel/plugin-transform-dotall-regex@^7.4.4":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.18.6.tgz#b286b3e7aae6c7b861e45bed0a2fafd6b1a4fef8"
  integrity sha1-soaz56rmx7hh5FvtCi+v1rGk/vg=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-duplicate-keys@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.18.9.tgz#687f15ee3cdad6d85191eb2a372c4528eaa0ae0e"
  integrity sha1-aH8V7jza1thRkesqNyxFKOqgrg4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-exponentiation-operator@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.18.6.tgz#421c705f4521888c65e91fdd1af951bfefd4dacd"
  integrity sha1-QhxwX0UhiIxl6R/dGvlRv+/U2s0=
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-for-of@^7.18.8":
  version "7.18.8"
  resolved "http://npm.htsc/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.18.8.tgz#6ef8a50b244eb6a0bdbad0c7c61877e4e30097c1"
  integrity sha1-bvilCyROtqC9utDHxhh35OMAl8E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-function-name@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.18.9.tgz#cc354f8234e62968946c61a46d6365440fc764e0"
  integrity sha1-zDVPgjTmKWiUbGGkbWNlRA/HZOA=
  dependencies:
    "@babel/helper-compilation-targets" "^7.18.9"
    "@babel/helper-function-name" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-literals@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.18.9.tgz#72796fdbef80e56fba3c6a699d54f0de557444bc"
  integrity sha1-cnlv2++A5W+6PGppnVTw3lV0RLw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-member-expression-literals@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.18.6.tgz#ac9fdc1a118620ac49b7e7a5d2dc177a1bfee88e"
  integrity sha1-rJ/cGhGGIKxJt+el0twXehv+6I4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-modules-amd@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.18.6.tgz#8c91f8c5115d2202f277549848874027d7172d21"
  integrity sha1-jJH4xRFdIgLyd1SYSIdAJ9cXLSE=
  dependencies:
    "@babel/helper-module-transforms" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.18.6":
  version "7.19.6"
  resolved "http://npm.htsc/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.19.6.tgz#25b32feef24df8038fc1ec56038917eacb0b730c"
  integrity sha1-JbMv7vJN+AOPwexWA4kX6ssLcww=
  dependencies:
    "@babel/helper-module-transforms" "^7.19.6"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-simple-access" "^7.19.4"

"@babel/plugin-transform-modules-systemjs@^7.19.0":
  version "7.19.0"
  resolved "http://npm.htsc/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.19.0.tgz#5f20b471284430f02d9c5059d9b9a16d4b085a1f"
  integrity sha1-XyC0cShEMPAtnFBZ2bmhbUsIWh8=
  dependencies:
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-module-transforms" "^7.19.0"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-validator-identifier" "^7.18.6"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.18.6.tgz#81d3832d6034b75b54e62821ba58f28ed0aab4b9"
  integrity sha1-gdODLWA0t1tU5ighuljyjtCqtLk=
  dependencies:
    "@babel/helper-module-transforms" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-named-capturing-groups-regex@^7.19.1":
  version "7.19.1"
  resolved "http://npm.htsc/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.19.1.tgz#ec7455bab6cd8fb05c525a94876f435a48128888"
  integrity sha1-7HRVurbNj7BcUlqUh29DWkgSiIg=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.19.0"
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-transform-new-target@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.18.6.tgz#d128f376ae200477f37c4ddfcc722a8a1b3246a8"
  integrity sha1-0Sjzdq4gBHfzfE3fzHIqihsyRqg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-object-super@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.18.6.tgz#fb3c6ccdd15939b6ff7939944b51971ddc35912c"
  integrity sha1-+zxszdFZObb/eTmUS1GXHdw1kSw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-replace-supers" "^7.18.6"

"@babel/plugin-transform-parameters@^7.18.8":
  version "7.18.8"
  resolved "http://npm.htsc/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.18.8.tgz#ee9f1a0ce6d78af58d0956a9378ea3427cccb48a"
  integrity sha1-7p8aDObXivWNCVapN46jQnzMtIo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-property-literals@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.18.6.tgz#e22498903a483448e94e032e9bbb9c5ccbfc93a3"
  integrity sha1-4iSYkDpINEjpTgMum7ucXMv8k6M=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-react-display-name@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.18.6.tgz#8b1125f919ef36ebdfff061d664e266c666b9415"
  integrity sha1-ixEl+RnvNuvf/wYdZk4mbGZrlBU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-react-jsx-development@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-react-jsx-development/download/@babel/plugin-transform-react-jsx-development-7.18.6.tgz#dbe5c972811e49c7405b630e4d0d2e1380c0ddc5"
  integrity sha1-2+XJcoEeScdAW2MOTQ0uE4DA3cU=
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.18.6"

"@babel/plugin-transform-react-jsx@^7.18.6":
  version "7.19.0"
  resolved "http://npm.htsc/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.19.0.tgz#b3cbb7c3a00b92ec8ae1027910e331ba5c500eb9"
  integrity sha1-s8u3w6ALkuyK4QJ5EOMxulxQDrk=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/plugin-syntax-jsx" "^7.18.6"
    "@babel/types" "^7.19.0"

"@babel/plugin-transform-react-pure-annotations@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-react-pure-annotations/download/@babel/plugin-transform-react-pure-annotations-7.18.6.tgz#561af267f19f3e5d59291f9950fd7b9663d0d844"
  integrity sha1-VhryZ/GfPl1ZKR+ZUP17lmPQ2EQ=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-regenerator@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.18.6.tgz#585c66cb84d4b4bf72519a34cfce761b8676ca73"
  integrity sha1-WFxmy4TUtL9yUZo0z852G4Z2ynM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    regenerator-transform "^0.15.0"

"@babel/plugin-transform-reserved-words@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.18.6.tgz#b1abd8ebf8edaa5f7fe6bbb8d2133d23b6a6f76a"
  integrity sha1-savY6/jtql9/5ru40hM9I7am92o=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-runtime@^7.18.0":
  version "7.19.6"
  resolved "http://npm.htsc/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.19.6.tgz#9d2a9dbf4e12644d6f46e5e75bfbf02b5d6e9194"
  integrity sha1-nSqdv04SZE1vRuXnW/vwK11ukZQ=
  dependencies:
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.19.0"
    babel-plugin-polyfill-corejs2 "^0.3.3"
    babel-plugin-polyfill-corejs3 "^0.6.0"
    babel-plugin-polyfill-regenerator "^0.4.1"
    semver "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.18.6.tgz#6d6df7983d67b195289be24909e3f12a8f664dc9"
  integrity sha1-bW33mD1nsZUom+JJCePxKo9mTck=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-spread@^7.19.0":
  version "7.19.0"
  resolved "http://npm.htsc/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.19.0.tgz#dd60b4620c2fec806d60cfaae364ec2188d593b6"
  integrity sha1-3WC0Ygwv7IBtYM+q42TsIYjVk7Y=
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"

"@babel/plugin-transform-sticky-regex@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.18.6.tgz#c6706eb2b1524028e317720339583ad0f444adcc"
  integrity sha1-xnBusrFSQCjjF3IDOVg60PRErcw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-template-literals@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.18.9.tgz#04ec6f10acdaa81846689d63fae117dd9c243a5e"
  integrity sha1-BOxvEKzaqBhGaJ1j+uEX3ZwkOl4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-typeof-symbol@^7.18.9":
  version "7.18.9"
  resolved "http://npm.htsc/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.18.9.tgz#c8cea68263e45addcd6afc9091429f80925762c0"
  integrity sha1-yM6mgmPkWt3NavyQkUKfgJJXYsA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-typescript@^7.18.0", "@babel/plugin-transform-typescript@^7.18.6":
  version "7.19.3"
  resolved "http://npm.htsc/@babel/plugin-transform-typescript/download/@babel/plugin-transform-typescript-7.19.3.tgz#4f1db1e0fe278b42ddbc19ec2f6cd2f8262e35d6"
  integrity sha1-Tx2x4P4ni0LdvBnsL2zS+CYuNdY=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.19.0"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/plugin-syntax-typescript" "^7.18.6"

"@babel/plugin-transform-unicode-escapes@^7.18.10":
  version "7.18.10"
  resolved "http://npm.htsc/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.18.10.tgz#1ecfb0eda83d09bbcb77c09970c2dd55832aa246"
  integrity sha1-Hs+w7ag9CbvLd8CZcMLdVYMqokY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-unicode-regex@^7.18.6":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.18.6.tgz#194317225d8c201bbae103364ffe9e2cea36cdca"
  integrity sha1-GUMXIl2MIBu64QM2T/6eLOo2zco=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/preset-env@^7.18.0":
  version "7.19.4"
  resolved "http://npm.htsc/@babel/preset-env/download/@babel/preset-env-7.19.4.tgz#4c91ce2e1f994f717efb4237891c3ad2d808c94b"
  integrity sha1-TJHOLh+ZT3F++0I3iRw60tgIyUs=
  dependencies:
    "@babel/compat-data" "^7.19.4"
    "@babel/helper-compilation-targets" "^7.19.3"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-validator-option" "^7.18.6"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.18.6"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.18.9"
    "@babel/plugin-proposal-async-generator-functions" "^7.19.1"
    "@babel/plugin-proposal-class-properties" "^7.18.6"
    "@babel/plugin-proposal-class-static-block" "^7.18.6"
    "@babel/plugin-proposal-dynamic-import" "^7.18.6"
    "@babel/plugin-proposal-export-namespace-from" "^7.18.9"
    "@babel/plugin-proposal-json-strings" "^7.18.6"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.18.9"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.18.6"
    "@babel/plugin-proposal-numeric-separator" "^7.18.6"
    "@babel/plugin-proposal-object-rest-spread" "^7.19.4"
    "@babel/plugin-proposal-optional-catch-binding" "^7.18.6"
    "@babel/plugin-proposal-optional-chaining" "^7.18.9"
    "@babel/plugin-proposal-private-methods" "^7.18.6"
    "@babel/plugin-proposal-private-property-in-object" "^7.18.6"
    "@babel/plugin-proposal-unicode-property-regex" "^7.18.6"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-import-assertions" "^7.18.6"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.18.6"
    "@babel/plugin-transform-async-to-generator" "^7.18.6"
    "@babel/plugin-transform-block-scoped-functions" "^7.18.6"
    "@babel/plugin-transform-block-scoping" "^7.19.4"
    "@babel/plugin-transform-classes" "^7.19.0"
    "@babel/plugin-transform-computed-properties" "^7.18.9"
    "@babel/plugin-transform-destructuring" "^7.19.4"
    "@babel/plugin-transform-dotall-regex" "^7.18.6"
    "@babel/plugin-transform-duplicate-keys" "^7.18.9"
    "@babel/plugin-transform-exponentiation-operator" "^7.18.6"
    "@babel/plugin-transform-for-of" "^7.18.8"
    "@babel/plugin-transform-function-name" "^7.18.9"
    "@babel/plugin-transform-literals" "^7.18.9"
    "@babel/plugin-transform-member-expression-literals" "^7.18.6"
    "@babel/plugin-transform-modules-amd" "^7.18.6"
    "@babel/plugin-transform-modules-commonjs" "^7.18.6"
    "@babel/plugin-transform-modules-systemjs" "^7.19.0"
    "@babel/plugin-transform-modules-umd" "^7.18.6"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.19.1"
    "@babel/plugin-transform-new-target" "^7.18.6"
    "@babel/plugin-transform-object-super" "^7.18.6"
    "@babel/plugin-transform-parameters" "^7.18.8"
    "@babel/plugin-transform-property-literals" "^7.18.6"
    "@babel/plugin-transform-regenerator" "^7.18.6"
    "@babel/plugin-transform-reserved-words" "^7.18.6"
    "@babel/plugin-transform-shorthand-properties" "^7.18.6"
    "@babel/plugin-transform-spread" "^7.19.0"
    "@babel/plugin-transform-sticky-regex" "^7.18.6"
    "@babel/plugin-transform-template-literals" "^7.18.9"
    "@babel/plugin-transform-typeof-symbol" "^7.18.9"
    "@babel/plugin-transform-unicode-escapes" "^7.18.10"
    "@babel/plugin-transform-unicode-regex" "^7.18.6"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.19.4"
    babel-plugin-polyfill-corejs2 "^0.3.3"
    babel-plugin-polyfill-corejs3 "^0.6.0"
    babel-plugin-polyfill-regenerator "^0.4.1"
    core-js-compat "^3.25.1"
    semver "^6.3.0"

"@babel/preset-modules@^0.1.5":
  version "0.1.5"
  resolved "http://npm.htsc/@babel/preset-modules/download/@babel/preset-modules-0.1.5.tgz#ef939d6e7f268827e1841638dc6ff95515e115d9"
  integrity sha1-75Odbn8miCfhhBY43G/5VRXhFdk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@^7.17.12":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/preset-react/download/@babel/preset-react-7.18.6.tgz#979f76d6277048dc19094c217b507f3ad517dd2d"
  integrity sha1-l5921idwSNwZCUwhe1B/OtUX3S0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-validator-option" "^7.18.6"
    "@babel/plugin-transform-react-display-name" "^7.18.6"
    "@babel/plugin-transform-react-jsx" "^7.18.6"
    "@babel/plugin-transform-react-jsx-development" "^7.18.6"
    "@babel/plugin-transform-react-pure-annotations" "^7.18.6"

"@babel/preset-typescript@^7.17.12":
  version "7.18.6"
  resolved "http://npm.htsc/@babel/preset-typescript/download/@babel/preset-typescript-7.18.6.tgz#ce64be3e63eddc44240c6358daefac17b3186399"
  integrity sha1-zmS+PmPt3EQkDGNY2u+sF7MYY5k=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-validator-option" "^7.18.6"
    "@babel/plugin-transform-typescript" "^7.18.6"

"@babel/runtime-corejs3@^7.10.2":
  version "7.19.6"
  resolved "http://npm.htsc/@babel/runtime-corejs3/download/@babel/runtime-corejs3-7.19.6.tgz#778471a71d915cf3b955a9201bebabfe924f872a"
  integrity sha1-d4Rxpx2RXPO5VakgG+ur/pJPhyo=
  dependencies:
    core-js-pure "^3.25.1"
    regenerator-runtime "^0.13.4"

"@babel/runtime@^7.10.2", "@babel/runtime@^7.18.0", "@babel/runtime@^7.18.9", "@babel/runtime@^7.8.4":
  version "7.20.0"
  resolved "http://npm.htsc/@babel/runtime/download/@babel/runtime-7.20.0.tgz#824a9ef325ffde6f78056059db3168c08785e24a"
  integrity sha1-gkqe8yX/3m94BWBZ2zFowIeF4ko=
  dependencies:
    regenerator-runtime "^0.13.10"

"@babel/template@^7.18.10", "@babel/template@^7.3.3":
  version "7.18.10"
  resolved "http://npm.htsc/@babel/template/download/@babel/template-7.18.10.tgz#6f9134835970d1dbf0835c0d100c9f38de0c5e71"
  integrity sha1-b5E0g1lw0dvwg1wNEAyfON4MXnE=
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/parser" "^7.18.10"
    "@babel/types" "^7.18.10"

"@babel/traverse@^7.18.11", "@babel/traverse@^7.19.1":
  version "7.20.0"
  resolved "http://npm.htsc/@babel/traverse/download/@babel/traverse-7.20.0.tgz#538c4c6ce6255f5666eba02252a7b59fc2d5ed98"
  integrity sha1-U4xMbOYlX1Zm66AiUqe1n8LV7Zg=
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.20.0"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.20.0"
    "@babel/types" "^7.20.0"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/traverse@^7.19.4", "@babel/traverse@^7.19.6", "@babel/traverse@^7.7.2":
  version "7.19.6"
  resolved "http://npm.htsc/@babel/traverse/download/@babel/traverse-7.19.6.tgz#7b4c865611df6d99cb131eec2e8ac71656a490dc"
  integrity sha1-e0yGVhHfbZnLEx7sLorHFlakkNw=
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.19.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.19.6"
    "@babel/types" "^7.19.4"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.18.10", "@babel/types@^7.18.6", "@babel/types@^7.19.0", "@babel/types@^7.19.4", "@babel/types@^7.3.0", "@babel/types@^7.3.3":
  version "7.19.4"
  resolved "http://npm.htsc/@babel/types/download/@babel/types-7.19.4.tgz#0dd5c91c573a202d600490a35b33246fed8a41c7"
  integrity sha1-DdXJHFc6IC1gBJCjWzMkb+2KQcc=
  dependencies:
    "@babel/helper-string-parser" "^7.19.4"
    "@babel/helper-validator-identifier" "^7.19.1"
    to-fast-properties "^2.0.0"

"@babel/types@^7.18.0", "@babel/types@^7.18.9", "@babel/types@^7.20.0", "@babel/types@^7.4.4":
  version "7.20.0"
  resolved "http://npm.htsc/@babel/types/download/@babel/types-7.20.0.tgz#52c94cf8a7e24e89d2a194c25c35b17a64871479"
  integrity sha1-UslM+KfiTonSoZTCXDWxemSHFHk=
  dependencies:
    "@babel/helper-string-parser" "^7.19.4"
    "@babel/helper-validator-identifier" "^7.19.1"
    to-fast-properties "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "http://npm.htsc/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz#75a2e8b51cb758a7553d6804a5932d7aace75c39"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@colors/colors@1.5.0":
  version "1.5.0"
  resolved "http://npm.htsc/@colors/colors/download/@colors/colors-1.5.0.tgz#bb504579c1cae923e6576a4f5da43d25f97bdbd9"
  integrity sha1-u1BFecHK6SPmV2pPXaQ9Jfl729k=

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "http://npm.htsc/@cspotcode/source-map-support/download/@cspotcode/source-map-support-0.8.1.tgz#00629c35a688e05a88b1cda684fb9d5e73f000a1"
  integrity sha1-AGKcNaaI4FqIsc2mhPudXnPwAKE=
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@csstools/selector-specificity@^2.0.2":
  version "2.0.2"
  resolved "http://npm.htsc/@csstools/selector-specificity/download/@csstools/selector-specificity-2.0.2.tgz#1bfafe4b7ed0f3e4105837e056e0a89b108ebe36"
  integrity sha1-G/r+S37Q8+QQWDfgVuComxCOvjY=

"@dabh/diagnostics@^2.0.2":
  version "2.0.3"
  resolved "http://npm.htsc/@dabh/diagnostics/download/@dabh/diagnostics-2.0.3.tgz#7f7e97ee9a725dffc7808d93668cc984e1dc477a"
  integrity sha1-f36X7ppyXf/HgI2TZozJhOHcR3o=
  dependencies:
    colorspace "1.1.x"
    enabled "2.0.x"
    kuler "^2.0.0"

"@eslint/eslintrc@^0.4.3":
  version "0.4.3"
  resolved "http://npm.htsc/@eslint/eslintrc/download/@eslint/eslintrc-0.4.3.tgz#9e42981ef035beb3dd49add17acb96e8ff6f394c"
  integrity sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw=
  dependencies:
    ajv "^6.12.4"
    debug "^4.1.1"
    espree "^7.3.0"
    globals "^13.9.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^3.13.1"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@graphql-tools/merge@8.4.0":
  version "8.4.0"
  resolved "http://npm.htsc/@graphql-tools/merge/download/@graphql-tools/merge-8.4.0.tgz#47fbe5c4b6764276dc35bd19c4e7d3c46d3dc0fc"
  integrity sha1-R/vlxLZ2QnbcNb0ZxOfTxG09wPw=
  dependencies:
    "@graphql-tools/utils" "9.2.1"
    tslib "^2.4.0"

"@graphql-tools/merge@^8.4.1":
  version "8.4.2"
  resolved "http://npm.htsc/@graphql-tools/merge/download/@graphql-tools/merge-8.4.2.tgz#95778bbe26b635e8d2f60ce9856b388f11fe8288"
  integrity sha1-lXeLvia2NejS9gzphWs4jxH+gog=
  dependencies:
    "@graphql-tools/utils" "^9.2.1"
    tslib "^2.4.0"

"@graphql-tools/schema@9.0.17":
  version "9.0.17"
  resolved "http://npm.htsc/@graphql-tools/schema/download/@graphql-tools/schema-9.0.17.tgz#d731e9899465f88d5b9bf69e607ec465bb88b062"
  integrity sha1-1zHpiZRl+I1bm/aeYH7EZbuIsGI=
  dependencies:
    "@graphql-tools/merge" "8.4.0"
    "@graphql-tools/utils" "9.2.1"
    tslib "^2.4.0"
    value-or-promise "1.0.12"

"@graphql-tools/schema@^9.0.0":
  version "9.0.19"
  resolved "http://npm.htsc/@graphql-tools/schema/download/@graphql-tools/schema-9.0.19.tgz#c4ad373b5e1b8a0cf365163435b7d236ebdd06e7"
  integrity sha1-xK03O14bigzzZRY0NbfSNuvdBuc=
  dependencies:
    "@graphql-tools/merge" "^8.4.1"
    "@graphql-tools/utils" "^9.2.1"
    tslib "^2.4.0"
    value-or-promise "^1.0.12"

"@graphql-tools/utils@9.2.1", "@graphql-tools/utils@^9.2.1":
  version "9.2.1"
  resolved "http://npm.htsc/@graphql-tools/utils/download/@graphql-tools/utils-9.2.1.tgz#1b3df0ef166cfa3eae706e3518b17d5922721c57"
  integrity sha1-Gz3w7xZs+j6ucG41GLF9WSJyHFc=
  dependencies:
    "@graphql-typed-document-node/core" "^3.1.1"
    tslib "^2.4.0"

"@graphql-tools/utils@^10.0.1":
  version "10.0.1"
  resolved "http://npm.htsc/@graphql-tools/utils/download/@graphql-tools/utils-10.0.1.tgz#52e6c0ce920b57473823e487184f5017974fe4c4"
  integrity sha1-UubAzpILV0c4I+SHGE9QF5dP5MQ=
  dependencies:
    "@graphql-typed-document-node/core" "^3.1.1"
    tslib "^2.4.0"

"@graphql-typed-document-node/core@^3.1.1":
  version "3.2.0"
  resolved "http://npm.htsc/@graphql-typed-document-node/core/download/@graphql-typed-document-node/core-3.2.0.tgz#5f3d96ec6b2354ad6d8a28bf216a1d97b5426861"
  integrity sha1-Xz2W7GsjVK1tiii/IWodl7VCaGE=

"@ht/eslint-config-htsc@2.0.10-beta.1":
  version "2.0.10-beta.1"
  resolved "http://npm.htsc/@ht/eslint-config-htsc/download/@ht/eslint-config-htsc-2.0.10-beta.1.tgz#70bb3de746ad624dafc908615659a5cd0a5a4665"
  integrity sha1-cLs950atYk2vyQhhVlmlzQpaRmU=
  dependencies:
    "@babel/eslint-parser" "^7.18.9"
    "@babel/eslint-plugin" "^7.13.10"
    "@modern-js-app/eslint-config" "1.2.4"
    "@typescript-eslint/eslint-plugin" "^5.12.1"
    "@typescript-eslint/parser" "^5.12.1"
    eslint "^7.32.0"
    eslint-config-airbnb "^18.2.1"
    eslint-config-prettier "^8.3.0"
    eslint-import-resolver-webpack "^0.13.1"
    eslint-plugin-eslint-comments "^3.1.1"
    eslint-plugin-filenames "^1.3.2"
    eslint-plugin-import "^2.18.2"
    eslint-plugin-jsx-a11y "^6.4.1"
    eslint-plugin-markdown "^2.2.0"
    eslint-plugin-node "^11.1.0"
    eslint-plugin-prettier "^3.4.1"
    eslint-plugin-promise "^5.1.0"
    eslint-plugin-react "^7.24.0"
    eslint-plugin-react-hooks "^4.2.0"
    eslint-plugin-vue "8.7.1"
    postcss "^8.4.16"
    postcss-html "^1.5.0"
    postcss-less "^6.0.0"
    prettier "^2.7.1"
    stylelint "^14.9.1"
    stylelint-config-css-modules "^4.1.0"
    stylelint-config-prettier "^9.0.3"
    stylelint-config-standard "^26.0.0"
    stylelint-declaration-block-no-ignored-properties "^2.5.0"
    stylelint-prettier "^2.0.0"
    typescript "*"

"@ht/node-trace@^0.1.5":
  version "0.1.5"
  resolved "http://registry.npm.htsc/@ht/node-trace/-/node-trace-0.1.5.tgz#b5686123fd9862bb20df37ad546c9ca0c39a47fb"
  integrity sha512-M3edER28jBw3SBo6QkVSqyIb5+vDtL9ZNVwumwLEoIPyGsGmdDrnua+ShaEUmCREhprl3xZ4pLPqv2bGnYvv1A==
  dependencies:
    cls-rtracer "^2.6.0"
    ctrip-apollo "^4.5.0"
    debug "^4.3.2"
    ip "^1.1.5"
    kafkajs "^1.15.0"

"@ht/winston-transport-octopus@0.1.3":
  version "0.1.3"
  resolved "http://npm.htsc/@ht/winston-transport-octopus/download/@ht/winston-transport-octopus-0.1.3.tgz#6ed52d7e561d4f0f8f14ca009ca77e9e9aeb7ff0"
  integrity sha1-btUtflYdTw+PFMoAnKd+nprrf/A=
  dependencies:
    ctrip-apollo "^4.5.0"
    ip "^1.1.5"
    kafkajs "^1.15.0"
    lodash "^4.17.21"
    protobufjs "^6.11.2"
    util "^0.12.4"
    winston-transport "^4.4.0"

"@humanwhocodes/config-array@^0.5.0":
  version "0.5.0"
  resolved "http://npm.htsc/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.5.0.tgz#1407967d4c6eecd7388f83acf1eaf4d0c6e58ef9"
  integrity sha1-FAeWfUxu7Nc4j4Os8er00Mbljvk=
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  version "1.2.1"
  resolved "http://npm.htsc/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.1.tgz#b520529ec21d8e5945a1851dfd1c32e94e39ff45"
  integrity sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U=

"@ioredis/commands@^1.1.1":
  version "1.2.0"
  resolved "http://npm.htsc/@ioredis/commands/download/@ioredis/commands-1.2.0.tgz#6d61b3097470af1fdbbe622795b8921d42018e11"
  integrity sha1-bWGzCXRwrx/bvmInlbiSHUIBjhE=

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "http://npm.htsc/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz#fd3db1d59ecf7cf121e80650bb86712f9b55eced"
  integrity sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  version "0.1.3"
  resolved "http://npm.htsc/@istanbuljs/schema/download/@istanbuljs/schema-0.1.3.tgz#e45e384e4b8ec16bce2fd903af78450f6bf7ec98"
  integrity sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=

"@jest/console@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/console/download/@jest/console-28.1.3.tgz#2030606ec03a18c31803b8a36382762e447655df"
  integrity sha1-IDBgbsA6GMMYA7ijY4J2LkR2Vd8=
  dependencies:
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^28.1.3"
    jest-util "^28.1.3"
    slash "^3.0.0"

"@jest/core@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/core/download/@jest/core-28.1.3.tgz#0ebf2bd39840f1233cd5f2d1e6fc8b71bd5a1ac7"
  integrity sha1-Dr8r05hA8SM81fLR5vyLcb1aGsc=
  dependencies:
    "@jest/console" "^28.1.3"
    "@jest/reporters" "^28.1.3"
    "@jest/test-result" "^28.1.3"
    "@jest/transform" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-changed-files "^28.1.3"
    jest-config "^28.1.3"
    jest-haste-map "^28.1.3"
    jest-message-util "^28.1.3"
    jest-regex-util "^28.0.2"
    jest-resolve "^28.1.3"
    jest-resolve-dependencies "^28.1.3"
    jest-runner "^28.1.3"
    jest-runtime "^28.1.3"
    jest-snapshot "^28.1.3"
    jest-util "^28.1.3"
    jest-validate "^28.1.3"
    jest-watcher "^28.1.3"
    micromatch "^4.0.4"
    pretty-format "^28.1.3"
    rimraf "^3.0.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/environment/download/@jest/environment-28.1.3.tgz#abed43a6b040a4c24fdcb69eab1f97589b2d663e"
  integrity sha1-q+1DprBApMJP3Laeqx+XWJstZj4=
  dependencies:
    "@jest/fake-timers" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    jest-mock "^28.1.3"

"@jest/expect-utils@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/expect-utils/download/@jest/expect-utils-28.1.3.tgz#58561ce5db7cd253a7edddbc051fb39dda50f525"
  integrity sha1-WFYc5dt80lOn7d28BR+zndpQ9SU=
  dependencies:
    jest-get-type "^28.0.2"

"@jest/expect@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/expect/download/@jest/expect-28.1.3.tgz#9ac57e1d4491baca550f6bdbd232487177ad6a72"
  integrity sha1-msV+HUSRuspVD2vb0jJIcXetanI=
  dependencies:
    expect "^28.1.3"
    jest-snapshot "^28.1.3"

"@jest/fake-timers@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/fake-timers/download/@jest/fake-timers-28.1.3.tgz#230255b3ad0a3d4978f1d06f70685baea91c640e"
  integrity sha1-IwJVs60KPUl48dBvcGhbrqkcZA4=
  dependencies:
    "@jest/types" "^28.1.3"
    "@sinonjs/fake-timers" "^9.1.2"
    "@types/node" "*"
    jest-message-util "^28.1.3"
    jest-mock "^28.1.3"
    jest-util "^28.1.3"

"@jest/globals@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/globals/download/@jest/globals-28.1.3.tgz#a601d78ddc5fdef542728309894895b4a42dc333"
  integrity sha1-pgHXjdxf3vVCcoMJiUiVtKQtwzM=
  dependencies:
    "@jest/environment" "^28.1.3"
    "@jest/expect" "^28.1.3"
    "@jest/types" "^28.1.3"

"@jest/reporters@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/reporters/download/@jest/reporters-28.1.3.tgz#9adf6d265edafc5fc4a434cfb31e2df5a67a369a"
  integrity sha1-mt9tJl7a/F/EpDTPsx4t9aZ6Npo=
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^28.1.3"
    "@jest/test-result" "^28.1.3"
    "@jest/transform" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@jridgewell/trace-mapping" "^0.3.13"
    "@types/node" "*"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^5.1.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.1.3"
    jest-message-util "^28.1.3"
    jest-util "^28.1.3"
    jest-worker "^28.1.3"
    slash "^3.0.0"
    string-length "^4.0.1"
    strip-ansi "^6.0.0"
    terminal-link "^2.0.0"
    v8-to-istanbul "^9.0.1"

"@jest/schemas@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/schemas/download/@jest/schemas-28.1.3.tgz#ad8b86a66f11f33619e3d7e1dcddd7f2d40ff905"
  integrity sha1-rYuGpm8R8zYZ49fh3N3X8tQP+QU=
  dependencies:
    "@sinclair/typebox" "^0.24.1"

"@jest/source-map@^28.1.2":
  version "28.1.2"
  resolved "http://npm.htsc/@jest/source-map/download/@jest/source-map-28.1.2.tgz#7fe832b172b497d6663cdff6c13b0a920e139e24"
  integrity sha1-f+gysXK0l9ZmPN/2wTsKkg4TniQ=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.13"
    callsites "^3.0.0"
    graceful-fs "^4.2.9"

"@jest/test-result@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/test-result/download/@jest/test-result-28.1.3.tgz#5eae945fd9f4b8fcfce74d239e6f725b6bf076c5"
  integrity sha1-Xq6UX9n0uPz8500jnm9yW2vwdsU=
  dependencies:
    "@jest/console" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/test-sequencer/download/@jest/test-sequencer-28.1.3.tgz#9d0c283d906ac599c74bde464bc0d7e6a82886c3"
  integrity sha1-nQwoPZBqxZnHS95GS8DX5qgohsM=
  dependencies:
    "@jest/test-result" "^28.1.3"
    graceful-fs "^4.2.9"
    jest-haste-map "^28.1.3"
    slash "^3.0.0"

"@jest/transform@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/transform/download/@jest/transform-28.1.3.tgz#59d8098e50ab07950e0f2fc0fc7ec462371281b0"
  integrity sha1-WdgJjlCrB5UODy/A/H7EYjcSgbA=
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/types" "^28.1.3"
    "@jridgewell/trace-mapping" "^0.3.13"
    babel-plugin-istanbul "^6.1.1"
    chalk "^4.0.0"
    convert-source-map "^1.4.0"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^28.1.3"
    jest-regex-util "^28.0.2"
    jest-util "^28.1.3"
    micromatch "^4.0.4"
    pirates "^4.0.4"
    slash "^3.0.0"
    write-file-atomic "^4.0.1"

"@jest/types@^28.1.3":
  version "28.1.3"
  resolved "http://npm.htsc/@jest/types/download/@jest/types-28.1.3.tgz#b05de80996ff12512bc5ceb1d208285a7d11748b"
  integrity sha1-sF3oCZb/ElErxc6x0ggoWn0RdIs=
  dependencies:
    "@jest/schemas" "^28.1.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@josephg/resolvable@^1.0.0":
  version "1.0.1"
  resolved "http://npm.htsc/@josephg/resolvable/download/@josephg/resolvable-1.0.1.tgz#69bc4db754d79e1a2f17a650d3466e038d94a5eb"
  integrity sha1-abxNt1TXnhovF6ZQ00ZuA42Upes=

"@jridgewell/gen-mapping@^0.1.0":
  version "0.1.1"
  resolved "http://npm.htsc/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.1.1.tgz#e5d2e450306a9491e3bd77e323e38d7aff315996"
  integrity sha1-5dLkUDBqlJHjvXfjI+ONev8xWZY=
  dependencies:
    "@jridgewell/set-array" "^1.0.0"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/gen-mapping@^0.3.0", "@jridgewell/gen-mapping@^0.3.2":
  version "0.3.2"
  resolved "http://npm.htsc/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.2.tgz#c1aedc61e853f2bb9f5dfe6d4442d3b565b253b9"
  integrity sha1-wa7cYehT8rufXf5tRELTtWWyU7k=
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0", "@jridgewell/resolve-uri@^3.0.3":
  version "3.1.0"
  resolved "http://npm.htsc/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.0.tgz#2203b118c157721addfe69d47b70465463066d78"
  integrity sha1-IgOxGMFXchrd/mnUe3BGVGMGbXg=

"@jridgewell/set-array@^1.0.0", "@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "http://npm.htsc/@jridgewell/set-array/download/@jridgewell/set-array-1.1.2.tgz#7c6cf998d6d20b914c0a55a91ae928ff25965e72"
  integrity sha1-fGz5mNbSC5FMClWpGuko/yWWXnI=

"@jridgewell/source-map@^0.3.2":
  version "0.3.2"
  resolved "http://npm.htsc/@jridgewell/source-map/download/@jridgewell/source-map-0.3.2.tgz#f45351aaed4527a298512ec72f81040c998580fb"
  integrity sha1-9FNRqu1FJ6KYUS7HL4EEDJmFgPs=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/sourcemap-codec@1.4.14", "@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.14"
  resolved "http://npm.htsc/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.4.14.tgz#add4c98d341472a289190b424efbdb096991bb24"
  integrity sha1-rdTJjTQUcqKJGQtCTvvbCWmRuyQ=

"@jridgewell/sourcemap-codec@^1.4.15":
  version "1.5.0"
  resolved "http://registry.npm.htsc/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz#3188bcb273a414b0d215fd22a58540b989b9409a"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  resolved "http://npm.htsc/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.9.tgz#6534fd5933a53ba7cbf3a17615e273a0d1273ff9"
  integrity sha1-ZTT9WTOlO6fL86F2FeJzoNEnP/k=
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/trace-mapping@^0.3.12", "@jridgewell/trace-mapping@^0.3.13", "@jridgewell/trace-mapping@^0.3.14", "@jridgewell/trace-mapping@^0.3.9":
  version "0.3.17"
  resolved "http://npm.htsc/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.17.tgz#793041277af9073b0951a7fe0f0d8c4c98c36985"
  integrity sha1-eTBBJ3r5BzsJUaf+Dw2MTJjDaYU=
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@liaoliaots/nestjs-redis@9.0.5":
  version "9.0.5"
  resolved "http://npm.htsc/@liaoliaots/nestjs-redis/download/@liaoliaots/nestjs-redis-9.0.5.tgz#8e8b1326792c83599425eca123bb3a93d6cef248"
  integrity sha1-josTJnksg1mUJeyhI7s6k9bO8kg=
  dependencies:
    tslib "2.4.1"

"@ljharb/through@^2.3.12":
  version "2.3.13"
  resolved "http://registry.npm.htsc/@ljharb/through/-/through-2.3.13.tgz#b7e4766e0b65aa82e529be945ab078de79874edc"
  integrity sha512-/gKJun8NNiWGZJkGzI/Ragc53cOdcLNdzjLaIa+GEjguQs0ulsurx8WN0jijdK9yPqDvziX995sMRLyLt1uZMQ==
  dependencies:
    call-bind "^1.0.7"

"@lukeed/csprng@^1.0.0":
  version "1.1.0"
  resolved "http://npm.htsc/@lukeed/csprng/download/@lukeed/csprng-1.1.0.tgz#1e3e4bd05c1cc7a0b2ddbd8a03f39f6e4b5e6cfe"
  integrity sha1-Hj5L0Fwcx6Cy3b2KA/OfbktebP4=

"@modern-js-app/eslint-config@1.2.4":
  version "1.2.4"
  resolved "http://npm.htsc/@modern-js-app/eslint-config/download/@modern-js-app/eslint-config-1.2.4.tgz#dff49b67ad8d3e2d0c141f5459fa48cf1bcba8d3"
  integrity sha1-3/SbZ62NPi0MFB9UWfpIzxvLqNM=
  dependencies:
    "@modern-js/babel-preset-app" "^1.2.0"

"@modern-js/babel-preset-app@^1.2.0":
  version "1.20.1"
  resolved "http://npm.htsc/@modern-js/babel-preset-app/download/@modern-js/babel-preset-app-1.20.1.tgz#b401b233a36cb517db1146c0df15797aae4a4ea7"
  integrity sha1-tAGyM6NstRfbEUbA3xV5eq5KTqc=
  dependencies:
    "@babel/core" "^7.18.0"
    "@babel/plugin-transform-destructuring" "^7.18.0"
    "@babel/runtime" "^7.18.0"
    "@babel/types" "^7.18.0"
    "@modern-js/babel-preset-base" "1.20.1"
    "@modern-js/utils" "1.20.1"
    core-js "^3.25.0"

"@modern-js/babel-preset-base@1.20.1":
  version "1.20.1"
  resolved "http://npm.htsc/@modern-js/babel-preset-base/download/@modern-js/babel-preset-base-1.20.1.tgz#ac49ccdc120c986f983b75c6e0739208e612ebcc"
  integrity sha1-rEnM3BIMmG+YO3XG4HOSCOYS68w=
  dependencies:
    "@babel/core" "^7.18.0"
    "@babel/plugin-transform-runtime" "^7.18.0"
    "@babel/plugin-transform-typescript" "^7.18.0"
    "@babel/preset-env" "^7.18.0"
    "@babel/preset-react" "^7.17.12"
    "@babel/preset-typescript" "^7.17.12"
    "@babel/runtime" "^7.18.0"
    "@modern-js/utils" "1.20.1"
    "@types/babel__core" "^7.1.16"
    cosmiconfig "^7.0.1"
    lodash "^4.17.21"
    resolve "^1.22.0"

"@modern-js/utils@1.20.1":
  version "1.20.1"
  resolved "http://npm.htsc/@modern-js/utils/download/@modern-js/utils-1.20.1.tgz#c39e35b1481889f6f6444c1c7429b95f52d89486"
  integrity sha1-w541sUgYifb2REwcdCm5X1LYlIY=
  dependencies:
    caniuse-lite "^1.0.30001332"
    lodash "^4.17.21"

"@nestjs/apollo@^11.0.3":
  version "11.0.3"
  resolved "http://npm.htsc/@nestjs/apollo/download/@nestjs/apollo-11.0.3.tgz#cb72e1dd2fd88e34d1308f472419d48da9e80d26"
  integrity sha1-y3Lh3S/YjjTRMI9HJBnUjanoDSY=
  dependencies:
    "@apollo/server-plugin-landing-page-graphql-playground" "4.0.0"
    iterall "1.3.0"
    lodash.omit "4.5.0"
    tslib "2.5.0"

"@nestjs/axios@^0.1.0":
  version "0.1.2"
  resolved "http://registry.npm.htsc/@nestjs/axios/-/axios-0.1.2.tgz#6bcfe23836d521739b4a80b0889a4a51561399bd"
  integrity sha512-KFW37K5ujSce3lukNp9Uym5T6/oKCDw8EQRYhAKdNiBveMtotkokJwKIHVKCc21qfXMyes4291eR2No1Sf/fow==
  dependencies:
    axios "1.2.1"

"@nestjs/cli@9.0.0":
  version "9.0.0"
  resolved "http://npm.htsc/@nestjs/cli/download/@nestjs/cli-9.0.0.tgz#f9ab4c33cfbc9ee54d792fe29e0f582e0bf04b06"
  integrity sha1-+atMM8+8nuVNeS/ing9YLgvwSwY=
  dependencies:
    "@angular-devkit/core" "14.0.5"
    "@angular-devkit/schematics" "14.0.5"
    "@angular-devkit/schematics-cli" "14.0.5"
    "@nestjs/schematics" "^9.0.0"
    chalk "3.0.0"
    chokidar "3.5.3"
    cli-table3 "0.6.2"
    commander "4.1.1"
    fork-ts-checker-webpack-plugin "7.2.11"
    inquirer "7.3.3"
    node-emoji "1.11.0"
    ora "5.4.1"
    os-name "4.0.1"
    rimraf "3.0.2"
    shelljs "0.8.5"
    source-map-support "0.5.21"
    tree-kill "1.2.2"
    tsconfig-paths "3.14.1"
    tsconfig-paths-webpack-plugin "3.5.2"
    typescript "4.7.4"
    webpack "5.73.0"
    webpack-node-externals "3.0.0"

"@nestjs/common@9.0.5":
  version "9.0.5"
  resolved "http://npm.htsc/@nestjs/common/download/@nestjs/common-9.0.5.tgz#cc082a0ef24c2693cf637f2758a357b4db2b6489"
  integrity sha1-zAgqDvJMJpPPY38nWKNXtNsrZIk=
  dependencies:
    iterare "1.2.1"
    tslib "2.4.0"
    uuid "8.3.2"

"@nestjs/config@2.2.0":
  version "2.2.0"
  resolved "http://npm.htsc/@nestjs/config/download/@nestjs/config-2.2.0.tgz#9f3da35f7c4a58724c0a0817d6f04b66e6703430"
  integrity sha1-nz2jX3xKWHJMCggX1vBLZuZwNDA=
  dependencies:
    dotenv "16.0.1"
    dotenv-expand "8.0.3"
    lodash "4.17.21"
    uuid "8.3.2"

"@nestjs/core@^9.4.0":
  version "9.4.0"
  resolved "http://npm.htsc/@nestjs/core/download/@nestjs/core-9.4.0.tgz#bca5128138fcf9b4668bc524b578f3805a325183"
  integrity sha1-vKUSgTj8+bRmi8UktXjzgFoyUYM=
  dependencies:
    uid "2.0.2"
    "@nuxtjs/opencollective" "0.3.2"
    fast-safe-stringify "2.1.1"
    iterare "1.2.1"
    path-to-regexp "3.2.0"
    tslib "2.5.0"

"@nestjs/graphql@^11.0.5":
  version "11.0.5"
  resolved "http://npm.htsc/@nestjs/graphql/download/@nestjs/graphql-11.0.5.tgz#6b66a57319ce5772f5991a6147c9520f254fc51a"
  integrity sha1-a2alcxnOV3L1mRphR8lSDyVPxRo=
  dependencies:
    "@graphql-tools/merge" "8.4.0"
    "@graphql-tools/schema" "9.0.17"
    "@graphql-tools/utils" "9.2.1"
    "@nestjs/mapped-types" "1.2.2"
    chokidar "3.5.3"
    fast-glob "3.2.12"
    graphql-tag "2.12.6"
    graphql-ws "5.12.1"
    lodash "4.17.21"
    normalize-path "3.0.0"
    subscriptions-transport-ws "0.11.0"
    tslib "2.5.0"
    uuid "9.0.0"
    ws "8.13.0"

"@nestjs/jwt@9.0.0":
  version "9.0.0"
  resolved "http://npm.htsc/@nestjs/jwt/download/@nestjs/jwt-9.0.0.tgz#73e01338d2853a55033528b540cfd92c7996bae9"
  integrity sha1-c+ATONKFOlUDNSi1QM/ZLHmWuuk=
  dependencies:
    "@types/jsonwebtoken" "8.5.8"
    jsonwebtoken "8.5.1"

"@nestjs/mapped-types@1.1.0":
  version "1.1.0"
  resolved "http://npm.htsc/@nestjs/mapped-types/download/@nestjs/mapped-types-1.1.0.tgz#54a9fa61079635dd6c3c75fd9593f20b2302a55b"
  integrity sha1-VKn6YQeWNd1sPHX9lZPyCyMCpVs=

"@nestjs/mapped-types@1.2.2":
  version "1.2.2"
  resolved "http://npm.htsc/@nestjs/mapped-types/download/@nestjs/mapped-types-1.2.2.tgz#d9ddb143776e309dbc1a518ac1607fddac1e140e"
  integrity sha1-2d2xQ3duMJ28GlGKwWB/3aweFA4=

"@nestjs/passport@9.0.0":
  version "9.0.0"
  resolved "http://npm.htsc/@nestjs/passport/download/@nestjs/passport-9.0.0.tgz#0571bb08f8043456bc6df44cd4f59ca5f10c9b9f"
  integrity sha1-BXG7CPgENFa8bfRM1PWcpfEMm58=

"@nestjs/platform-express@9.0.5":
  version "9.0.5"
  resolved "http://npm.htsc/@nestjs/platform-express/download/@nestjs/platform-express-9.0.5.tgz#e271b0a93a6f137b55ddb5fd4eea542b36a1db89"
  integrity sha1-4nGwqTpvE3tV3bX9TupUKzah24k=
  dependencies:
    body-parser "1.20.0"
    cors "2.8.5"
    express "4.18.1"
    multer "1.4.4-lts.1"
    tslib "2.4.0"

"@nestjs/schematics@9.0.1", "@nestjs/schematics@^9.0.0":
  version "9.0.1"
  resolved "http://npm.htsc/@nestjs/schematics/download/@nestjs/schematics-9.0.1.tgz#2ec1b3fc4cd2c44310d4d7d1f5f276a18d24964b"
  integrity sha1-LsGz/EzSxEMQ1NfR9fJ2oY0klks=
  dependencies:
    "@angular-devkit/core" "14.0.5"
    "@angular-devkit/schematics" "14.0.5"
    fs-extra "10.1.0"
    jsonc-parser "3.0.0"
    pluralize "8.0.0"

"@nestjs/swagger@6.0.4":
  version "6.0.4"
  resolved "http://npm.htsc/@nestjs/swagger/download/@nestjs/swagger-6.0.4.tgz#36c472c92f12b0af5094a5b54dbf1ac81e5b3640"
  integrity sha1-NsRyyS8SsK9QlKW1Tb8ayB5bNkA=
  dependencies:
    "@nestjs/mapped-types" "1.1.0"
    js-yaml "4.1.0"
    lodash "4.17.21"
    path-to-regexp "3.2.0"
    swagger-ui-dist "4.12.0"

"@nestjs/testing@^9.3.12":
  version "9.4.3"
  resolved "http://npm.htsc/@nestjs/testing/-/testing-9.4.3.tgz#53ffbabdd38f500b145c30f2fbb76dedad393d79"
  integrity sha512-LDT8Ai2eKnTzvnPaJwWOK03qTaFap5uHHsJCv6dL0uKWk6hyF9jms8DjyVaGsaujCaXDG8izl1mDEER0OmxaZA==
  dependencies:
    tslib "2.5.3"

"@nestjs/typeorm@9.0.1":
  version "9.0.1"
  resolved "http://npm.htsc/@nestjs/typeorm/download/@nestjs/typeorm-9.0.1.tgz#f78bfc00e71731ea860288e4a03830107daf3d9c"
  integrity sha1-94v8AOcXMeqGAojkoDgwEH2vPZw=
  dependencies:
    uuid "8.3.2"

"@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
  version "5.1.1-v1"
  resolved "http://npm.htsc/@nicolo-ribaudo/eslint-scope-5-internals/download/@nicolo-ribaudo/eslint-scope-5-internals-5.1.1-v1.tgz#dbf733a965ca47b1973177dc0bb6c889edcfb129"
  integrity sha1-2/czqWXKR7GXMXfcC7bIie3PsSk=
  dependencies:
    eslint-scope "5.1.1"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://npm.htsc/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://npm.htsc/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "http://npm.htsc/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@nuxtjs/opencollective@0.3.2":
  version "0.3.2"
  resolved "http://npm.htsc/@nuxtjs/opencollective/download/@nuxtjs/opencollective-0.3.2.tgz#620ce1044f7ac77185e825e1936115bb38e2681c"
  integrity sha1-YgzhBE96x3GF6CXhk2EVuzjiaBw=
  dependencies:
    chalk "^4.1.0"
    consola "^2.15.0"
    node-fetch "^2.6.1"

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  version "1.1.2"
  resolved "http://npm.htsc/@protobufjs/aspromise/download/@protobufjs/aspromise-1.1.2.tgz#9b8b0cc663d669a7d8f6f5d0893a14d348f30fbf"
  integrity sha1-m4sMxmPWaafY9vXQiToU00jzD78=

"@protobufjs/base64@^1.1.2":
  version "1.1.2"
  resolved "http://npm.htsc/@protobufjs/base64/download/@protobufjs/base64-1.1.2.tgz#4c85730e59b9a1f1f349047dbf24296034bb2735"
  integrity sha1-TIVzDlm5ofHzSQR9vyQpYDS7JzU=

"@protobufjs/codegen@^2.0.4":
  version "2.0.4"
  resolved "http://npm.htsc/@protobufjs/codegen/download/@protobufjs/codegen-2.0.4.tgz#7ef37f0d010fb028ad1ad59722e506d9262815cb"
  integrity sha1-fvN/DQEPsCitGtWXIuUG2SYoFcs=

"@protobufjs/eventemitter@^1.1.0":
  version "1.1.0"
  resolved "http://npm.htsc/@protobufjs/eventemitter/download/@protobufjs/eventemitter-1.1.0.tgz#355cbc98bafad5978f9ed095f397621f1d066b70"
  integrity sha1-NVy8mLr61ZePntCV85diHx0Ga3A=

"@protobufjs/fetch@^1.1.0":
  version "1.1.0"
  resolved "http://npm.htsc/@protobufjs/fetch/download/@protobufjs/fetch-1.1.0.tgz#ba99fb598614af65700c1619ff06d454b0d84c45"
  integrity sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  version "1.0.2"
  resolved "http://npm.htsc/@protobufjs/float/download/@protobufjs/float-1.0.2.tgz#5e9e1abdcb73fc0a7cb8b291df78c8cbd97b87d1"
  integrity sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E=

"@protobufjs/inquire@^1.1.0":
  version "1.1.0"
  resolved "http://npm.htsc/@protobufjs/inquire/download/@protobufjs/inquire-1.1.0.tgz#ff200e3e7cf2429e2dcafc1140828e8cc638f089"
  integrity sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik=

"@protobufjs/path@^1.1.2":
  version "1.1.2"
  resolved "http://npm.htsc/@protobufjs/path/download/@protobufjs/path-1.1.2.tgz#6cc2b20c5c9ad6ad0dccfd21ca7673d8d7fbf68d"
  integrity sha1-bMKyDFya1q0NzP0hynZz2Nf79o0=

"@protobufjs/pool@^1.1.0":
  version "1.1.0"
  resolved "http://npm.htsc/@protobufjs/pool/download/@protobufjs/pool-1.1.0.tgz#09fd15f2d6d3abfa9b65bc366506d6ad7846ff54"
  integrity sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q=

"@protobufjs/utf8@^1.1.0":
  version "1.1.0"
  resolved "http://npm.htsc/@protobufjs/utf8/download/@protobufjs/utf8-1.1.0.tgz#a777360b5b39a1a2e5106f8e858f2fd2d060c570"
  integrity sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA=

"@sinclair/typebox@^0.24.1":
  version "0.24.28"
  resolved "http://npm.htsc/@sinclair/typebox/download/@sinclair/typebox-0.24.28.tgz#15aa0b416f82c268b1573ab653e4413c965fe794"
  integrity sha1-FaoLQW+CwmixVzq2U+RBPJZf55Q=

"@sinonjs/commons@^1.7.0":
  version "1.8.3"
  resolved "http://npm.htsc/@sinonjs/commons/download/@sinonjs/commons-1.8.3.tgz#3802ddd21a50a949b6721ddd72da36e67e7f1b2d"
  integrity sha1-OALd0hpQqUm2ch3dcto25n5/Gy0=
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^9.1.2":
  version "9.1.2"
  resolved "http://npm.htsc/@sinonjs/fake-timers/download/@sinonjs/fake-timers-9.1.2.tgz#4eaab737fab77332ab132d396a3c0d364bd0ea8c"
  integrity sha1-Tqq3N/q3czKrEy05ajwNNkvQ6ow=
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@sqltools/formatter@^1.2.2":
  version "1.2.3"
  resolved "http://npm.htsc/@sqltools/formatter/download/@sqltools/formatter-1.2.3.tgz#1185726610acc37317ddab11c3c7f9066966bd20"
  integrity sha1-EYVyZhCsw3MX3asRw8f5BmlmvSA=

"@ts-morph/common@~0.16.0":
  version "0.16.0"
  resolved "http://npm.htsc/@ts-morph/common/download/@ts-morph/common-0.16.0.tgz#57e27d4b3fd65a4cd72cb36679ed08acb40fa3ba"
  integrity sha1-V+J9Sz/WWkzXLLNmee0IrLQPo7o=
  dependencies:
    fast-glob "^3.2.11"
    minimatch "^5.1.0"
    mkdirp "^1.0.4"
    path-browserify "^1.0.1"

"@tsconfig/node10@^1.0.7":
  version "1.0.9"
  resolved "http://npm.htsc/@tsconfig/node10/download/@tsconfig/node10-1.0.9.tgz#df4907fc07a886922637b15e02d4cebc4c0021b2"
  integrity sha1-30kH/AeohpImN7FeAtTOvEwAIbI=

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "http://npm.htsc/@tsconfig/node12/download/@tsconfig/node12-1.0.11.tgz#ee3def1f27d9ed66dac6e46a295cffb0152e058d"
  integrity sha1-7j3vHyfZ7WbaxuRqKVz/sBUuBY0=

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "http://npm.htsc/@tsconfig/node14/download/@tsconfig/node14-1.0.3.tgz#e4386316284f00b98435bf40f72f75a09dabf6c1"
  integrity sha1-5DhjFihPALmENb9A9y91oJ2r9sE=

"@tsconfig/node16@^1.0.2":
  version "1.0.3"
  resolved "http://npm.htsc/@tsconfig/node16/download/@tsconfig/node16-1.0.3.tgz#472eaab5f15c1ffdd7f8628bd4c4f753995ec79e"
  integrity sha1-Ry6qtfFcH/3X+GKL1MT3U5lex54=

"@types/babel__core@^7.1.14", "@types/babel__core@^7.1.16":
  version "7.1.19"
  resolved "http://npm.htsc/@types/babel__core/download/@types/babel__core-7.1.19.tgz#7b497495b7d1b4812bdb9d02804d0576f43ee460"
  integrity sha1-e0l0lbfRtIEr250CgE0FdvQ+5GA=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.4"
  resolved "http://npm.htsc/@types/babel__generator/download/@types/babel__generator-7.6.4.tgz#1f20ce4c5b1990b37900b63f050182d28c2439b7"
  integrity sha1-HyDOTFsZkLN5ALY/BQGC0owkObc=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.1"
  resolved "http://npm.htsc/@types/babel__template/download/@types/babel__template-7.4.1.tgz#3d1a48fd9d6c0edfd56f2ff578daed48f36c8969"
  integrity sha1-PRpI/Z1sDt/Vby/1eNrtSPNsiWk=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.6":
  version "7.18.2"
  resolved "http://npm.htsc/@types/babel__traverse/download/@types/babel__traverse-7.18.2.tgz#235bf339d17185bdec25e024ca19cce257cc7309"
  integrity sha1-I1vzOdFxhb3sJeAkyhnM4lfMcwk=
  dependencies:
    "@babel/types" "^7.3.0"

"@types/body-parser@*":
  version "1.19.2"
  resolved "http://npm.htsc/@types/body-parser/download/@types/body-parser-1.19.2.tgz#aea2059e28b7658639081347ac4fab3de166e6f0"
  integrity sha1-rqIFnii3ZYY5CBNHrE+rPeFm5vA=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/chance@1.1.3":
  version "1.1.3"
  resolved "http://npm.htsc/@types/chance/download/@types/chance-1.1.3.tgz#d19fe9391288d60fdccd87632bfc9ab2b4523fea"
  integrity sha1-0Z/pORKI1g/czYdjK/yasrRSP+o=

"@types/connect@*":
  version "3.4.35"
  resolved "http://npm.htsc/@types/connect/download/@types/connect-3.4.35.tgz#5fcf6ae445e4021d1fc2219a4873cc73a3bb2ad1"
  integrity sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=
  dependencies:
    "@types/node" "*"

"@types/cookiejar@*":
  version "2.1.2"
  resolved "http://npm.htsc/@types/cookiejar/download/@types/cookiejar-2.1.2.tgz#66ad9331f63fe8a3d3d9d8c6e3906dd10f6446e8"
  integrity sha1-Zq2TMfY/6KPT2djG45Bt0Q9kRug=

"@types/eslint-scope@^3.7.3":
  version "3.7.4"
  resolved "http://npm.htsc/@types/eslint-scope/download/@types/eslint-scope-3.7.4.tgz#37fc1223f0786c39627068a12e94d6e6fc61de16"
  integrity sha1-N/wSI/B4bDlicGihLpTW5vxh3hY=
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "8.4.6"
  resolved "http://npm.htsc/@types/eslint/download/@types/eslint-8.4.6.tgz#7976f054c1bccfcf514bff0564c0c41df5c08207"
  integrity sha1-eXbwVMG8z89RS/8FZMDEHfXAggc=
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*":
  version "1.0.0"
  resolved "http://npm.htsc/@types/estree/download/@types/estree-1.0.0.tgz#5fb2e536c1ae9bf35366eed879e827fa59ca41c2"
  integrity sha1-X7LlNsGum/NTZu7Yeegn+lnKQcI=

"@types/estree@^0.0.51":
  version "0.0.51"
  resolved "http://npm.htsc/@types/estree/download/@types/estree-0.0.51.tgz#cfd70924a25a3fd32b218e5e420e6897e1ac4f40"
  integrity sha1-z9cJJKJaP9MrIY5eQg5ol+GsT0A=

"@types/express-serve-static-core@^4.17.18":
  version "4.17.31"
  resolved "http://npm.htsc/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.31.tgz#a1139efeab4e7323834bb0226e62ac019f474b2f"
  integrity sha1-oROe/qtOcyODS7AibmKsAZ9HSy8=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express-serve-static-core@^4.17.30", "@types/express-serve-static-core@^4.17.33":
  version "4.17.35"
  resolved "http://npm.htsc/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.35.tgz#c95dd4424f0d32e525d23812aa8ab8e4d3906c4f"
  integrity sha1-yV3UQk8NMuUl0jgSqoq45NOQbE8=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@4.17.13":
  version "4.17.13"
  resolved "http://npm.htsc/@types/express/download/@types/express-4.17.13.tgz#a76e2995728999bab51a33fabce1d705a3709034"
  integrity sha1-p24plXKJmbq1GjP6vOHXBaNwkDQ=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/express@^4.17.13":
  version "4.17.17"
  resolved "http://npm.htsc/@types/express/download/@types/express-4.17.17.tgz#01d5437f6ef9cfa8668e616e13c2f2ac9a491ae4"
  integrity sha1-AdVDf275z6hmjmFuE8LyrJpJGuQ=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/graceful-fs@^4.1.3":
  version "4.1.5"
  resolved "http://npm.htsc/@types/graceful-fs/download/@types/graceful-fs-4.1.5.tgz#21ffba0d98da4350db64891f92a9e5db3cdb4e15"
  integrity sha1-If+6DZjaQ1DbZIkfkqnl2zzbThU=
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.4"
  resolved "http://npm.htsc/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.4.tgz#8467d4b3c087805d63580480890791277ce35c44"
  integrity sha1-hGfUs8CHgF1jWASAiQeRJ3zjXEQ=

"@types/istanbul-lib-report@*":
  version "3.0.0"
  resolved "http://npm.htsc/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.0.tgz#c14c24f18ea8190c118ee7562b7ff99a36552686"
  integrity sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.1"
  resolved "http://npm.htsc/@types/istanbul-reports/download/@types/istanbul-reports-3.0.1.tgz#9153fe98bba2bd565a63add9436d6f0d7f8468ff"
  integrity sha1-kVP+mLuivVZaY63ZQ21vDX+EaP8=
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@28.1.6":
  version "28.1.6"
  resolved "http://npm.htsc/@types/jest/download/@types/jest-28.1.6.tgz#d6a9cdd38967d2d746861fb5be6b120e38284dd4"
  integrity sha1-1qnN04ln0tdGhh+1vmsSDjgoTdQ=
  dependencies:
    jest-matcher-utils "^28.0.0"
    pretty-format "^28.0.0"

"@types/json-schema@*", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.11"
  resolved "http://npm.htsc/@types/json-schema/download/@types/json-schema-7.0.11.tgz#d421b6c527a3037f7c84433fd2c4229e016863d3"
  integrity sha1-1CG2xSejA398hEM/0sQingFoY9M=

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "http://npm.htsc/@types/json5/download/@types/json5-0.0.29.tgz#ee28707ae94e11d2b827bcbe5270bcea7f3e71ee"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/jsonwebtoken@8.5.8":
  version "8.5.8"
  resolved "http://npm.htsc/@types/jsonwebtoken/download/@types/jsonwebtoken-8.5.8.tgz#01b39711eb844777b7af1d1f2b4cf22fda1c0c44"
  integrity sha1-AbOXEeuER3e3rx0fK0zyL9ocDEQ=
  dependencies:
    "@types/node" "*"

"@types/long@^4.0.0", "@types/long@^4.0.1":
  version "4.0.2"
  resolved "http://npm.htsc/@types/long/download/@types/long-4.0.2.tgz#b74129719fc8d11c01868010082d483b7545591a"
  integrity sha1-t0EpcZ/I0RwBhoAQCC1IO3VFWRo=

"@types/mdast@^3.0.0":
  version "3.0.10"
  resolved "http://npm.htsc/@types/mdast/download/@types/mdast-3.0.10.tgz#4724244a82a4598884cbbe9bcfd73dff927ee8af"
  integrity sha1-RyQkSoKkWYiEy76bz9c9/5J+6K8=
  dependencies:
    "@types/unist" "*"

"@types/mime@*":
  version "3.0.1"
  resolved "http://npm.htsc/@types/mime/download/@types/mime-3.0.1.tgz#5f8f2bca0a5863cb69bc0b0acd88c96cb1d4ae10"
  integrity sha1-X48rygpYY8tpvAsKzYjJbLHUrhA=

"@types/mime@^1":
  version "1.3.2"
  resolved "http://npm.htsc/@types/mime/download/@types/mime-1.3.2.tgz#93e25bf9ee75fe0fd80b594bc4feb0e862111b5a"
  integrity sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o=

"@types/minimist@^1.2.0":
  version "1.2.2"
  resolved "http://npm.htsc/@types/minimist/download/@types/minimist-1.2.2.tgz#ee771e2ba4b3dc5b372935d549fd9617bf345b8c"
  integrity sha1-7nceK6Sz3Fs3KTXVSf2WF780W4w=

"@types/node-fetch@^2.6.1":
  version "2.6.4"
  resolved "http://npm.htsc/@types/node-fetch/download/@types/node-fetch-2.6.4.tgz#1bc3a26de814f6bf466b25aeb1473fa1afe6a660"
  integrity sha1-G8OibegU9r9GayWusUc/oa/mpmA=
  dependencies:
    "@types/node" "*"
    form-data "^3.0.0"

"@types/node@*", "@types/node@^18.0.3":
  version "18.11.7"
  resolved "http://npm.htsc/@types/node/download/@types/node-18.11.7.tgz#8ccef136f240770c1379d50100796a6952f01f94"
  integrity sha1-jM7xNvJAdwwTedUBAHlqaVLwH5Q=

"@types/node@>=13.7.0":
  version "18.11.9"
  resolved "http://npm.htsc/@types/node/download/@types/node-18.11.9.tgz#02d013de7058cea16d36168ef2fc653464cfbad4"
  integrity sha1-AtAT3nBYzqFtNhaO8vxlNGTPutQ=

"@types/normalize-package-data@^2.4.0":
  version "2.4.1"
  resolved "http://npm.htsc/@types/normalize-package-data/download/@types/normalize-package-data-2.4.1.tgz#d3357479a0fdfdd5907fe67e17e0a85c906e1301"
  integrity sha1-0zV0eaD9/dWQf+Z+F+CoXJBuEwE=

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "http://npm.htsc/@types/parse-json/download/@types/parse-json-4.0.0.tgz#2f8bb441434d163b35fb8ffdccd7138927ffb8c0"
  integrity sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA=

"@types/prettier@^2.1.5":
  version "2.7.1"
  resolved "http://npm.htsc/@types/prettier/download/@types/prettier-2.7.1.tgz#dfd20e2dc35f027cdd6c1908e80a5ddc7499670e"
  integrity sha1-39IOLcNfAnzdbBkI6Apd3HSZZw4=

"@types/qs@*":
  version "6.9.7"
  resolved "http://npm.htsc/@types/qs/download/@types/qs-6.9.7.tgz#63bb7d067db107cc1e457c303bc25d511febf6cb"
  integrity sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss=

"@types/range-parser@*":
  version "1.2.4"
  resolved "http://npm.htsc/@types/range-parser/download/@types/range-parser-1.2.4.tgz#cd667bcfdd025213aafb7ca5915a932590acdcdc"
  integrity sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw=

"@types/semver@^7.3.12":
  version "7.3.13"
  resolved "http://npm.htsc/@types/semver/download/@types/semver-7.3.13.tgz#da4bfd73f49bd541d28920ab0e2bf0ee80f71c91"
  integrity sha1-2kv9c/Sb1UHSiSCrDivw7oD3HJE=

"@types/send@*":
  version "0.17.1"
  resolved "http://npm.htsc/@types/send/download/@types/send-0.17.1.tgz#ed4932b8a2a805f1fe362a70f4e62d0ac994e301"
  integrity sha1-7UkyuKKoBfH+Nipw9OYtCsmU4wE=
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  version "1.15.0"
  resolved "http://npm.htsc/@types/serve-static/download/@types/serve-static-1.15.0.tgz#c7930ff61afb334e121a9da780aac0d9b8f34155"
  integrity sha1-x5MP9hr7M04SGp2ngKrA2bjzQVU=
  dependencies:
    "@types/mime" "*"
    "@types/node" "*"

"@types/stack-utils@^2.0.0":
  version "2.0.1"
  resolved "http://npm.htsc/@types/stack-utils/download/@types/stack-utils-2.0.1.tgz#20f18294f797f2209b5f65c8e3b5c8e8261d127c"
  integrity sha1-IPGClPeX8iCbX2XI47XI6CYdEnw=

"@types/superagent@*":
  version "4.1.24"
  resolved "http://registry.npm.htsc/@types/superagent/-/superagent-4.1.24.tgz#e1f9ad3b66a21ed13c047e8529009735732dde0a"
  integrity sha512-mEafCgyKiMFin24SDzWN7yAADt4gt6YawFiNMp0QS5ZPboORfyxFt0s3VzJKhTaKg9py/4FUmrHLTNfJKt9Rbw==
  dependencies:
    "@types/cookiejar" "*"
    "@types/node" "*"

"@types/supertest@2.0.12":
  version "2.0.12"
  resolved "http://npm.htsc/@types/supertest/download/@types/supertest-2.0.12.tgz#ddb4a0568597c9aadff8dbec5b2e8fddbe8692fc"
  integrity sha1-3bSgVoWXyarf+NvsWy6P3b6Gkvw=
  dependencies:
    "@types/superagent" "*"

"@types/unist@*", "@types/unist@^2.0.2":
  version "2.0.6"
  resolved "http://npm.htsc/@types/unist/download/@types/unist-2.0.6.tgz#250a7b16c3b91f672a24552ec64678eeb1d3a08d"
  integrity sha1-JQp7FsO5H2cqJFUuxkZ47rHToI0=

"@types/yargs-parser@*":
  version "21.0.0"
  resolved "http://npm.htsc/@types/yargs-parser/download/@types/yargs-parser-21.0.0.tgz#0c60e537fa790f5f9472ed2776c2b71ec117351b"
  integrity sha1-DGDlN/p5D1+Ucu0ndsK3HsEXNRs=

"@types/yargs@^17.0.8":
  version "17.0.13"
  resolved "http://npm.htsc/@types/yargs/download/@types/yargs-17.0.13.tgz#34cced675ca1b1d51fcf4d34c3c6f0fa142a5c76"
  integrity sha1-NMztZ1yhsdUfz000w8bw+hQqXHY=
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^5.12.1":
  version "5.41.0"
  resolved "http://npm.htsc/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-5.41.0.tgz#f8eeb1c6bb2549f795f3ba71aec3b38d1ab6b1e1"
  integrity sha1-+O6xxrslSfeV87pxrsOzjRq2seE=
  dependencies:
    "@typescript-eslint/scope-manager" "5.41.0"
    "@typescript-eslint/type-utils" "5.41.0"
    "@typescript-eslint/utils" "5.41.0"
    debug "^4.3.4"
    ignore "^5.2.0"
    regexpp "^3.2.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/parser@^5.12.1":
  version "5.41.0"
  resolved "http://npm.htsc/@typescript-eslint/parser/download/@typescript-eslint/parser-5.41.0.tgz#0414a6405007e463dc527b459af1f19430382d67"
  integrity sha1-BBSmQFAH5GPcUntFmvHxlDA4LWc=
  dependencies:
    "@typescript-eslint/scope-manager" "5.41.0"
    "@typescript-eslint/types" "5.41.0"
    "@typescript-eslint/typescript-estree" "5.41.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.41.0":
  version "5.41.0"
  resolved "http://npm.htsc/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.41.0.tgz#28e3a41d626288d0628be14cf9de8d49fc30fadf"
  integrity sha1-KOOkHWJiiNBii+FM+d6NSfww+t8=
  dependencies:
    "@typescript-eslint/types" "5.41.0"
    "@typescript-eslint/visitor-keys" "5.41.0"

"@typescript-eslint/type-utils@5.41.0":
  version "5.41.0"
  resolved "http://npm.htsc/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-5.41.0.tgz#2371601171e9f26a4e6da918a7913f7266890cdf"
  integrity sha1-I3FgEXHp8mpObakYp5E/cmaJDN8=
  dependencies:
    "@typescript-eslint/typescript-estree" "5.41.0"
    "@typescript-eslint/utils" "5.41.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.41.0":
  version "5.41.0"
  resolved "http://npm.htsc/@typescript-eslint/types/download/@typescript-eslint/types-5.41.0.tgz#6800abebc4e6abaf24cdf220fb4ce28f4ab09a85"
  integrity sha1-aACr68Tmq68kzfIg+0zij0qwmoU=

"@typescript-eslint/typescript-estree@5.41.0":
  version "5.41.0"
  resolved "http://npm.htsc/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.41.0.tgz#bf5c6b3138adbdc73ba4871d060ae12c59366c61"
  integrity sha1-v1xrMTitvcc7pIcdBgrhLFk2bGE=
  dependencies:
    "@typescript-eslint/types" "5.41.0"
    "@typescript-eslint/visitor-keys" "5.41.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.41.0":
  version "5.41.0"
  resolved "http://npm.htsc/@typescript-eslint/utils/download/@typescript-eslint/utils-5.41.0.tgz#f41ae5883994a249d00b2ce69f4188f3a23fa0f9"
  integrity sha1-9BrliDmUoknQCyzmn0GI86I/oPk=
  dependencies:
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.41.0"
    "@typescript-eslint/types" "5.41.0"
    "@typescript-eslint/typescript-estree" "5.41.0"
    eslint-scope "^5.1.1"
    eslint-utils "^3.0.0"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@5.41.0":
  version "5.41.0"
  resolved "http://npm.htsc/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.41.0.tgz#d3510712bc07d5540160ed3c0f8f213b73e3bcd9"
  integrity sha1-01EHErwH1VQBYO08D48hO3PjvNk=
  dependencies:
    "@typescript-eslint/types" "5.41.0"
    eslint-visitor-keys "^3.3.0"

"@webassemblyjs/ast@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/ast/download/@webassemblyjs/ast-1.11.1.tgz#2bfd767eae1a6996f432ff7e8d7fc75679c0b6a7"
  integrity sha1-K/12fq4aaZb0Mv9+jX/HVnnAtqc=
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"

"@webassemblyjs/floating-point-hex-parser@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.11.1.tgz#f6c61a705f0fd7a6aecaa4e8198f23d9dc179e4f"
  integrity sha1-9sYacF8P16auyqToGY8j2dwXnk8=

"@webassemblyjs/helper-api-error@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.11.1.tgz#1a63192d8788e5c012800ba6a7a46c705288fd16"
  integrity sha1-GmMZLYeI5cASgAump6RscFKI/RY=

"@webassemblyjs/helper-buffer@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.11.1.tgz#832a900eb444884cde9a7cad467f81500f5e5ab5"
  integrity sha1-gyqQDrREiEzemnytRn+BUA9eWrU=

"@webassemblyjs/helper-numbers@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/helper-numbers/download/@webassemblyjs/helper-numbers-1.11.1.tgz#64d81da219fbbba1e3bd1bfc74f6e8c4e10a62ae"
  integrity sha1-ZNgdohn7u6HjvRv8dPboxOEKYq4=
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.11.1.tgz#f328241e41e7b199d0b20c18e88429c4433295e1"
  integrity sha1-8ygkHkHnsZnQsgwY6IQpxEMyleE=

"@webassemblyjs/helper-wasm-section@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.11.1.tgz#21ee065a7b635f319e738f0dd73bfbda281c097a"
  integrity sha1-Ie4GWntjXzGec48N1zv72igcCXo=
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"

"@webassemblyjs/ieee754@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.11.1.tgz#963929e9bbd05709e7e12243a099180812992614"
  integrity sha1-ljkp6bvQVwnn4SJDoJkYCBKZJhQ=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.11.1.tgz#ce814b45574e93d76bae1fb2644ab9cdd9527aa5"
  integrity sha1-zoFLRVdOk9drrh+yZEq5zdlSeqU=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.11.1.tgz#d1f8b764369e7c6e6bae350e854dec9a59f0a3ff"
  integrity sha1-0fi3ZDaefG5rrjUOhU3smlnwo/8=

"@webassemblyjs/wasm-edit@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.11.1.tgz#ad206ebf4bf95a058ce9880a8c092c5dec8193d6"
  integrity sha1-rSBuv0v5WgWM6YgKjAksXeyBk9Y=
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/helper-wasm-section" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-opt" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    "@webassemblyjs/wast-printer" "1.11.1"

"@webassemblyjs/wasm-gen@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.11.1.tgz#86c5ea304849759b7d88c47a32f4f039ae3c8f76"
  integrity sha1-hsXqMEhJdZt9iMR6MvTwOa48j3Y=
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wasm-opt@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.11.1.tgz#657b4c2202f4cf3b345f8a4c6461c8c2418985f2"
  integrity sha1-ZXtMIgL0zzs0X4pMZGHIwkGJhfI=
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"

"@webassemblyjs/wasm-parser@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.11.1.tgz#86ca734534f417e9bd3c67c7a1c75d8be41fb199"
  integrity sha1-hspzRTT0F+m9PGfHocddi+QfsZk=
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wast-printer@1.11.1":
  version "1.11.1"
  resolved "http://npm.htsc/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.11.1.tgz#d0c73beda8eec5426f10ae8ef55cee5e7084c2f0"
  integrity sha1-0Mc77ajuxUJvEK6O9VzuXnCEwvA=
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "http://npm.htsc/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "http://npm.htsc/@xtuc/long/download/@xtuc/long-4.2.2.tgz#d291c6a4e97989b5c61d9acf396ae4fe133a718d"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

accepts@~1.3.8:
  version "1.3.8"
  resolved "http://npm.htsc/accepts/download/accepts-1.3.8.tgz#0bf0be125b67014adcb0b0921e62db7bffe16b2e"
  integrity sha1-C/C+EltnAUrcsLCSHmLbe//hay4=
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-import-assertions@^1.7.6:
  version "1.8.0"
  resolved "http://npm.htsc/acorn-import-assertions/download/acorn-import-assertions-1.8.0.tgz#ba2b5939ce62c238db6d93d81c9b111b29b855e9"
  integrity sha1-uitZOc5iwjjbbZPYHJsRGym4Vek=

acorn-jsx@^5.3.1, acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "http://npm.htsc/acorn-jsx/download/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^8.1.1:
  version "8.2.0"
  resolved "http://npm.htsc/acorn-walk/download/acorn-walk-8.2.0.tgz#741210f2e2426454508853a2f44d0ab83b7f69c1"
  integrity sha1-dBIQ8uJCZFRQiFOi9E0KuDt/acE=

acorn@^7.4.0:
  version "7.4.1"
  resolved "http://npm.htsc/acorn/download/acorn-7.4.1.tgz#feaed255973d2e77555b83dbc08851a6c63520fa"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

acorn@^8.4.1, acorn@^8.5.0, acorn@^8.8.0:
  version "8.8.1"
  resolved "http://npm.htsc/acorn/download/acorn-8.8.1.tgz#0a3f9cbecc4ec3bea6f0a80b66ae8dd2da250b73"
  integrity sha1-Cj+cvsxOw76m8KgLZq6N0tolC3M=

ajv-formats@2.1.1:
  version "2.1.1"
  resolved "http://npm.htsc/ajv-formats/download/ajv-formats-2.1.1.tgz#6e669400659eb74973bbf2e33327180a0996b520"
  integrity sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "http://npm.htsc/ajv-keywords/download/ajv-keywords-3.5.2.tgz#31f29da5ab6e00d1c2d329acf7b5929614d5014d"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv@8.11.0, ajv@^8.0.0, ajv@^8.0.1:
  version "8.11.0"
  resolved "http://npm.htsc/ajv/download/ajv-8.11.0.tgz#977e91dd96ca669f54a11e23e378e33b884a565f"
  integrity sha1-l36R3ZbKZp9UoR4j43jjO4hKVl8=
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ajv@8.12.0:
  version "8.12.0"
  resolved "http://registry.npm.htsc/ajv/-/ajv-8.12.0.tgz#d1a0527323e22f53562c567c00991577dfbe19d1"
  integrity sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ajv@^6.10.0, ajv@^6.12.3, ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "http://npm.htsc/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-colors@4.1.1:
  version "4.1.1"
  resolved "http://npm.htsc/ansi-colors/download/ansi-colors-4.1.1.tgz#cbb9ae256bf750af1eab344f229aa27fe94ba348"
  integrity sha1-y7muJWv3UK8eqzRPIpqif+lLo0g=

ansi-colors@4.1.3, ansi-colors@^4.1.1:
  version "4.1.3"
  resolved "http://registry.npm.htsc/ansi-colors/-/ansi-colors-4.1.3.tgz#37611340eb2243e70cc604cad35d63270d48781b"
  integrity sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==

ansi-escapes@^4.2.1, ansi-escapes@^4.3.2:
  version "4.3.2"
  resolved "http://registry.npm.htsc/ansi-escapes/-/ansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://npm.htsc/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://npm.htsc/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://npm.htsc/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "http://npm.htsc/ansi-styles/download/ansi-styles-5.2.0.tgz#07449690ad45777d1924ac2abb2fc8895dba836b"
  integrity sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=

any-promise@^1.0.0:
  version "1.3.0"
  resolved "http://npm.htsc/any-promise/download/any-promise-1.3.0.tgz#abc6afeedcea52e809cdc0376aed3ce39635d17f"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^3.0.3, anymatch@~3.1.2:
  version "3.1.2"
  resolved "http://npm.htsc/anymatch/download/anymatch-3.1.2.tgz#c0557c096af32f106198f4f4e2a383537e378716"
  integrity sha1-wFV8CWrzLxBhmPT04qODU343hxY=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

app-root-path@^3.0.0:
  version "3.1.0"
  resolved "http://npm.htsc/app-root-path/download/app-root-path-3.1.0.tgz#5971a2fc12ba170369a7a1ef018c71e6e47c2e86"
  integrity sha1-WXGi/BK6FwNpp6HvAYxx5uR8LoY=

append-field@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/append-field/download/append-field-1.0.0.tgz#1e3440e915f0b1203d23748e78edd7b9b5b43e56"
  integrity sha1-HjRA6RXwsSA9I3SOeO3XubW0PlY=

arg@^4.1.0:
  version "4.1.3"
  resolved "http://npm.htsc/arg/download/arg-4.1.3.tgz#269fc7ad5b8e42cb63c896d5666017261c144089"
  integrity sha1-Jp/HrVuOQstjyJbVZmAXJhwUQIk=

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://npm.htsc/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

aria-query@^4.2.2:
  version "4.2.2"
  resolved "http://npm.htsc/aria-query/download/aria-query-4.2.2.tgz#0d2ca6c9aceb56b8977e9fed6aed7e15bbd2f83b"
  integrity sha1-DSymyazrVriXfp/tau1+FbvS+Ds=
  dependencies:
    "@babel/runtime" "^7.10.2"
    "@babel/runtime-corejs3" "^7.10.2"

array-find@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/array-find/download/array-find-1.0.0.tgz#6c8e286d11ed768327f8e62ecee87353ca3e78b8"
  integrity sha1-bI4obRHtdoMn+OYuzuhzU8o+eLg=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/array-flatten/download/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-includes@^3.1.4, array-includes@^3.1.5:
  version "3.1.5"
  resolved "http://npm.htsc/array-includes/download/array-includes-3.1.5.tgz#2c320010db8d31031fd2a5f6b3bbd4b1aad31bdb"
  integrity sha1-LDIAENuNMQMf0qX2s7vUsarTG9s=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.19.5"
    get-intrinsic "^1.1.1"
    is-string "^1.0.7"

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/array-union/download/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array.prototype.flat@^1.2.5:
  version "1.3.0"
  resolved "http://npm.htsc/array.prototype.flat/download/array.prototype.flat-1.3.0.tgz#0b0c1567bf57b38b56b4c97b8aa72ab45e4adc7b"
  integrity sha1-CwwVZ79Xs4tWtMl7iqcqtF5K3Hs=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.2"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.0:
  version "1.3.0"
  resolved "http://npm.htsc/array.prototype.flatmap/download/array.prototype.flatmap-1.3.0.tgz#a7e8ed4225f4788a70cd910abcf0791e76a5534f"
  integrity sha1-p+jtQiX0eIpwzZEKvPB5HnalU08=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.2"
    es-shim-unscopables "^1.0.0"

arrify@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/arrify/download/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

asap@^2.0.0:
  version "2.0.6"
  resolved "http://npm.htsc/asap/download/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asn1@~0.2.3:
  version "0.2.6"
  resolved "http://npm.htsc/asn1/download/asn1-0.2.6.tgz#0d3a7bb6e64e02a90c0303b31f292868ea09a08d"
  integrity sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

ast-types-flow@^0.0.7:
  version "0.0.7"
  resolved "http://npm.htsc/ast-types-flow/download/ast-types-flow-0.0.7.tgz#f70b735c6bca1a5c9c22d982c3e39e7feba3bdad"
  integrity sha1-9wtzXGvKGlycItmCw+Oef+ujva0=

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/astral-regex/download/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

async-retry@^1.2.1:
  version "1.3.3"
  resolved "http://npm.htsc/async-retry/download/async-retry-1.3.3.tgz#0e7f36c04d8478e7a58bdbed80cedf977785f280"
  integrity sha1-Dn82wE2EeOeli9vtgM7fl3eF8oA=
  dependencies:
    retry "0.13.1"

async@^3.2.3:
  version "3.2.4"
  resolved "http://npm.htsc/async/download/async-3.2.4.tgz#2d22e00f8cddeb5fde5dd33522b56d1cf569a81c"
  integrity sha1-LSLgD4zd61/eXdM1IrVtHPVpqBw=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://npm.htsc/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

available-typed-arrays@^1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/available-typed-arrays/download/available-typed-arrays-1.0.5.tgz#92f95616501069d07d10edb2fc37d3e1c65123b7"
  integrity sha1-kvlWFlAQadB9EO2y/DfT4cZRI7c=

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "http://npm.htsc/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.11.0"
  resolved "http://npm.htsc/aws4/download/aws4-1.11.0.tgz#d61f46d83b2519250e2784daf5b09479a8b41c59"
  integrity sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk=

axe-core@^4.4.3:
  version "4.4.3"
  resolved "http://npm.htsc/axe-core/download/axe-core-4.4.3.tgz#11c74d23d5013c0fa5d183796729bc3482bd2f6f"
  integrity sha1-EcdNI9UBPA+l0YN5Zym8NIK9L28=

axios@1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.htsc/axios/-/axios-1.2.1.tgz#44cf04a3c9f0c2252ebd85975361c026cb9f864a"
  integrity sha512-I88cFiGu9ryt/tfVEi4kX2SITsvDddTajXTOFmt2uK1ZVA8LytjtdeyefdQWEf5PU8w+4SSJDoYnggflB5tW4A==
  dependencies:
    follow-redirects "^1.15.0"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axios@^1.3.4:
  version "1.7.7"
  resolved "http://registry.npm.htsc/axios/-/axios-1.7.7.tgz#2f554296f9892a72ac8d8e4c5b79c14a91d0a47f"
  integrity sha512-S4kL7XrjgBmvdGut0sN3yJxqYzrDOnivkBiN0OFs6hLiUam3UPvswUo0kqGyhqUZGEOytHyumEdXsAkgCOUf3Q==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axobject-query@^2.2.0:
  version "2.2.0"
  resolved "http://npm.htsc/axobject-query/download/axobject-query-2.2.0.tgz#943d47e10c0b704aa42275e20edf3722648989be"
  integrity sha1-lD1H4QwLcEqkInXiDt83ImSJib4=

babel-jest@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/babel-jest/download/babel-jest-28.1.3.tgz#c1187258197c099072156a0a121c11ee1e3917d5"
  integrity sha1-wRhyWBl8CZByFWoKEhwR7h45F9U=
  dependencies:
    "@jest/transform" "^28.1.3"
    "@types/babel__core" "^7.1.14"
    babel-plugin-istanbul "^6.1.1"
    babel-preset-jest "^28.1.3"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    slash "^3.0.0"

babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "http://npm.htsc/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz#84fda19c976ec5c6defef57f9427b3def66e17a3"
  integrity sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=
  dependencies:
    object.assign "^4.1.0"

babel-plugin-istanbul@^6.1.1:
  version "6.1.1"
  resolved "http://npm.htsc/babel-plugin-istanbul/download/babel-plugin-istanbul-6.1.1.tgz#fa88ec59232fd9b4e36dbbc540a8ec9a9b47da73"
  integrity sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-28.1.3.tgz#1952c4d0ea50f2d6d794353762278d1d8cca3fbe"
  integrity sha1-GVLE0OpQ8tbXlDU3YieNHYzKP74=
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.1.14"
    "@types/babel__traverse" "^7.0.6"

babel-plugin-polyfill-corejs2@^0.3.3:
  version "0.3.3"
  resolved "http://npm.htsc/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.3.3.tgz#5d1bd3836d0a19e1b84bbf2d9640ccb6f951c122"
  integrity sha1-XRvTg20KGeG4S78tlkDMtvlRwSI=
  dependencies:
    "@babel/compat-data" "^7.17.7"
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    semver "^6.1.1"

babel-plugin-polyfill-corejs3@^0.6.0:
  version "0.6.0"
  resolved "http://npm.htsc/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.6.0.tgz#56ad88237137eade485a71b52f72dbed57c6230a"
  integrity sha1-Vq2II3E36t5IWnG1L3Lb7VfGIwo=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    core-js-compat "^3.25.1"

babel-plugin-polyfill-regenerator@^0.4.1:
  version "0.4.1"
  resolved "http://npm.htsc/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.4.1.tgz#390f91c38d90473592ed43351e801a9d3e0fd747"
  integrity sha1-OQ+Rw42QRzWS7UM1HoAanT4P10c=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"

babel-preset-current-node-syntax@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-1.0.1.tgz#b4399239b89b2a011f9ddbe3e4f401fc40cff73b"
  integrity sha1-tDmSObibKgEfndvj5PQB/EDP9zs=
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

babel-preset-jest@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/babel-preset-jest/download/babel-preset-jest-28.1.3.tgz#5dfc20b99abed5db994406c2b9ab94c73aaa419d"
  integrity sha1-XfwguZq+1duZRAbCuauUxzqqQZ0=
  dependencies:
    babel-plugin-jest-hoist "^28.1.3"
    babel-preset-current-node-syntax "^1.0.0"

backo2@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/backo2/download/backo2-1.0.2.tgz#31ab1ac8b129363463e35b3ebb69f4dfcfba7947"
  integrity sha1-MasayLEpNjRj41s+u2n038+6eUc=

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://npm.htsc/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

balanced-match@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/balanced-match/download/balanced-match-2.0.0.tgz#dc70f920d78db8b858535795867bf48f820633d9"
  integrity sha1-3HD5INeNuLhYU1eVhnv0j4IGM9k=

base64-js@^1.3.1:
  version "1.5.1"
  resolved "http://npm.htsc/base64-js/download/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "http://npm.htsc/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "http://npm.htsc/binary-extensions/download/binary-extensions-2.2.0.tgz#75f502eeaf9ffde42fc98829645be4ea76bd9e2d"
  integrity sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=

bl@^1.0.0:
  version "1.2.3"
  resolved "http://npm.htsc/bl/download/bl-1.2.3.tgz#1e8dd80142eac80d7158c9dccc047fb620e035e7"
  integrity sha1-Ho3YAULqyA1xWMnczAR/tiDgNec=
  dependencies:
    readable-stream "^2.3.5"
    safe-buffer "^5.1.1"

bl@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/bl/download/bl-4.1.0.tgz#451535264182bec2fbbc83a62ab98cf11d9f7b3a"
  integrity sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

bluebird@^3.7.2:
  version "3.7.2"
  resolved "http://npm.htsc/bluebird/download/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

body-parser@1.20.0:
  version "1.20.0"
  resolved "http://npm.htsc/body-parser/download/body-parser-1.20.0.tgz#3de69bd89011c11573d7bfee6a64f11b6bd27cc5"
  integrity sha1-Peab2JARwRVz17/uamTxG2vSfMU=
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.10.3"
    raw-body "2.5.1"
    type-is "~1.6.18"
    unpipe "1.0.0"

body-parser@1.20.1:
  version "1.20.1"
  resolved "http://npm.htsc/body-parser/download/body-parser-1.20.1.tgz#b1812a8912c195cd371a3ee5e66faa2338a5c668"
  integrity sha1-sYEqiRLBlc03Gj7l5m+qIzilxmg=
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.1"
    type-is "~1.6.18"
    unpipe "1.0.0"

body-parser@^1.20.0:
  version "1.20.2"
  resolved "http://npm.htsc/body-parser/download/body-parser-1.20.2.tgz#6feb0e21c4724d06de7ff38da36dad4f57a747fd"
  integrity sha1-b+sOIcRyTQbef/ONo22tT1enR/0=
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/boolbase/download/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://npm.htsc/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/brace-expansion/download/brace-expansion-2.0.1.tgz#1edc459e0f0c548486ecf9fc99f2221364b9a0ae"
  integrity sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "http://npm.htsc/braces/download/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

browserslist@^4.14.5, browserslist@^4.21.3, browserslist@^4.21.4:
  version "4.21.4"
  resolved "http://npm.htsc/browserslist/download/browserslist-4.21.4.tgz#e7496bbc67b9e39dd0f98565feccdcb0d4ff6987"
  integrity sha1-50lrvGe5453Q+YVl/szcsNT/aYc=
  dependencies:
    caniuse-lite "^1.0.30001400"
    electron-to-chromium "^1.4.251"
    node-releases "^2.0.6"
    update-browserslist-db "^1.0.9"

bs-logger@0.x:
  version "0.2.6"
  resolved "http://npm.htsc/bs-logger/download/bs-logger-0.2.6.tgz#eb7d365307a72cf974cc6cda76b68354ad336bd8"
  integrity sha1-6302UwenLPl0zGzadraDVK0za9g=
  dependencies:
    fast-json-stable-stringify "2.x"

bser@2.1.1:
  version "2.1.1"
  resolved "http://npm.htsc/bser/download/bser-2.1.1.tgz#e6787da20ece9d07998533cfd9de6f5c38f4bc05"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-alloc-unsafe@^1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/buffer-alloc-unsafe/download/buffer-alloc-unsafe-1.1.0.tgz#bd7dc26ae2972d0eda253be061dba992349c19f0"
  integrity sha1-vX3CauKXLQ7aJTvgYdupkjScGfA=

buffer-alloc@^1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/buffer-alloc/download/buffer-alloc-1.2.0.tgz#890dd90d923a873e08e10e5fd51a57e5b7cce0ec"
  integrity sha1-iQ3ZDZI6hz4I4Q5f1RpX5bfM4Ow=
  dependencies:
    buffer-alloc-unsafe "^1.1.0"
    buffer-fill "^1.0.0"

buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "http://npm.htsc/buffer-crc32/download/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"
  integrity sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=

buffer-equal-constant-time@1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/buffer-equal-constant-time/download/buffer-equal-constant-time-1.0.1.tgz#f8e71132f7ffe6e01a5c9697a4c6f3e48d5cc819"
  integrity sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk=

buffer-fill@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/buffer-fill/download/buffer-fill-1.0.0.tgz#f8f78b76789888ef39f205cd637f68e702122b2c"
  integrity sha1-+PeLdniYiO858gXNY39o5wISKyw=

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "http://npm.htsc/buffer-from/download/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer-writer@2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/buffer-writer/download/buffer-writer-2.0.0.tgz#ce7eb81a38f7829db09c873f2fbb792c0c98ec04"
  integrity sha1-zn64Gjj3gp2wnIc/L7t5LAyY7AQ=

buffer@^5.5.0:
  version "5.7.1"
  resolved "http://npm.htsc/buffer/download/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffer@^6.0.3:
  version "6.0.3"
  resolved "http://npm.htsc/buffer/download/buffer-6.0.3.tgz#2ace578459cc8fbe2a70aaa8f52ee63b6a74c6c6"
  integrity sha1-Ks5XhFnMj74qcKqo9S7mO2p0xsY=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

busboy@^1.0.0:
  version "1.6.0"
  resolved "http://npm.htsc/busboy/download/busboy-1.6.0.tgz#966ea36a9502e43cdb9146962523b92f531f6893"
  integrity sha1-lm6japUC5DzbkUaWJSO5L1MfaJM=
  dependencies:
    streamsearch "^1.1.0"

bytes@3.1.2:
  version "3.1.2"
  resolved "http://npm.htsc/bytes/download/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/call-bind/download/call-bind-1.0.2.tgz#b1d4e89e688119c3c9a903ad30abb2f6a919be3c"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

call-bind@^1.0.7:
  version "1.0.7"
  resolved "http://registry.npm.htsc/call-bind/-/call-bind-1.0.7.tgz#06016599c40c56498c18769d2730be242b6fa3b9"
  integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://npm.htsc/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase-keys@^6.2.2:
  version "6.2.2"
  resolved "http://npm.htsc/camelcase-keys/download/camelcase-keys-6.2.2.tgz#5e755d6ba51aa223ec7d3d52f25778210f9dc3c0"
  integrity sha1-XnVda6UaoiPsfT1S8ld4IQ+dw8A=
  dependencies:
    camelcase "^5.3.1"
    map-obj "^4.0.0"
    quick-lru "^4.0.1"

camelcase@^5.3.1:
  version "5.3.1"
  resolved "http://npm.htsc/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.2.0:
  version "6.3.0"
  resolved "http://npm.htsc/camelcase/download/camelcase-6.3.0.tgz#5685b95eb209ac9c0c177467778c9c84df58ba9a"
  integrity sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=

caniuse-lite@^1.0.30001332:
  version "1.0.30001426"
  resolved "http://npm.htsc/caniuse-lite/download/caniuse-lite-1.0.30001426.tgz#58da20446ccd0cb1dfebd11d2350c907ee7c2eaa"
  integrity sha1-WNogRGzNDLHf69EdI1DJB+58Lqo=

caniuse-lite@^1.0.30001400:
  version "1.0.30001420"
  resolved "http://npm.htsc/caniuse-lite/download/caniuse-lite-1.0.30001420.tgz#f62f35f051e0b6d25532cf376776d41e45b47ef6"
  integrity sha1-9i818FHgttJVMs83Z3bUHkW0fvY=

caseless@~0.12.0:
  version "0.12.0"
  resolved "http://npm.htsc/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

chalk@3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/chalk/download/chalk-3.0.0.tgz#3f73c2bf526591f574cc492c51e2456349f844e4"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^2.0.0:
  version "2.4.2"
  resolved "http://npm.htsc/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.0.2, chalk@^4.1.0, chalk@^4.1.1, chalk@^4.1.2:
  version "4.1.2"
  resolved "http://npm.htsc/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^5.3.0:
  version "5.3.0"
  resolved "http://registry.npm.htsc/chalk/-/chalk-5.3.0.tgz#67c20a7ebef70e7f3970a01f90fa210cb6860385"
  integrity sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==

chance@1.1.8:
  version "1.1.8"
  resolved "http://npm.htsc/chance/download/chance-1.1.8.tgz#5d6c2b78c9170bf6eb9df7acdda04363085be909"
  integrity sha1-XWwreMkXC/brnfes3aBDYwhb6Qk=

char-regex@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/char-regex/download/char-regex-1.0.2.tgz#d744358226217f981ed58f479b1d6bcc29545dcf"
  integrity sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=

character-entities-legacy@^1.0.0:
  version "1.1.4"
  resolved "http://npm.htsc/character-entities-legacy/download/character-entities-legacy-1.1.4.tgz#94bc1845dce70a5bb9d2ecc748725661293d8fc1"
  integrity sha1-lLwYRdznClu50uzHSHJWYSk9j8E=

character-entities@^1.0.0:
  version "1.2.4"
  resolved "http://npm.htsc/character-entities/download/character-entities-1.2.4.tgz#e12c3939b7eaf4e5b15e7ad4c5e28e1d48c5b16b"
  integrity sha1-4Sw5Obfq9OWxXnrUxeKOHUjFsWs=

character-reference-invalid@^1.0.0:
  version "1.1.4"
  resolved "http://npm.htsc/character-reference-invalid/download/character-reference-invalid-1.1.4.tgz#083329cda0eae272ab3dbbf37e9a382c13af1560"
  integrity sha1-CDMpzaDq4nKrPbvzfpo4LBOvFWA=

chardet@^0.7.0:
  version "0.7.0"
  resolved "http://npm.htsc/chardet/download/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

chokidar@3.5.3, chokidar@^3.5.3:
  version "3.5.3"
  resolved "http://npm.htsc/chokidar/download/chokidar-3.5.3.tgz#1cf37c8707b932bd1af1ae22c0432e2acd1903bd"
  integrity sha1-HPN8hwe5Mr0a8a4iwEMuKs0ZA70=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chrome-trace-event@^1.0.2:
  version "1.0.3"
  resolved "http://npm.htsc/chrome-trace-event/download/chrome-trace-event-1.0.3.tgz#1015eced4741e15d06664a957dbbf50d041e26ac"
  integrity sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw=

ci-info@^3.2.0:
  version "3.5.0"
  resolved "http://npm.htsc/ci-info/download/ci-info-3.5.0.tgz#bfac2a29263de4c829d806b1ab478e35091e171f"
  integrity sha1-v6wqKSY95Mgp2Aaxq0eONQkeFx8=

cjs-module-lexer@^1.0.0:
  version "1.2.2"
  resolved "http://npm.htsc/cjs-module-lexer/download/cjs-module-lexer-1.2.2.tgz#9f84ba3244a512f3a54e5277e8eef4c489864e40"
  integrity sha1-n4S6MkSlEvOlTlJ36O70xImGTkA=

class-transformer@0.5.1:
  version "0.5.1"
  resolved "http://npm.htsc/class-transformer/download/class-transformer-0.5.1.tgz#24147d5dffd2a6cea930a3250a677addf96ab336"
  integrity sha1-JBR9Xf/Sps6pMKMlCmd63flqszY=

class-validator@0.13.2:
  version "0.13.2"
  resolved "http://npm.htsc/class-validator/download/class-validator-0.13.2.tgz#64b031e9f3f81a1e1dcd04a5d604734608b24143"
  integrity sha1-ZLAx6fP4Gh4dzQSl1gRzRgiyQUM=
  dependencies:
    libphonenumber-js "^1.9.43"
    validator "^13.7.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://npm.htsc/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-highlight@^2.1.11:
  version "2.1.11"
  resolved "http://npm.htsc/cli-highlight/download/cli-highlight-2.1.11.tgz#49736fa452f0aaf4fae580e30acb26828d2dc1bf"
  integrity sha1-SXNvpFLwqvT65YDjCssmgo0twb8=
  dependencies:
    chalk "^4.0.0"
    highlight.js "^10.7.1"
    mz "^2.4.0"
    parse5 "^5.1.1"
    parse5-htmlparser2-tree-adapter "^6.0.0"
    yargs "^16.0.0"

cli-spinners@^2.5.0:
  version "2.7.0"
  resolved "http://npm.htsc/cli-spinners/download/cli-spinners-2.7.0.tgz#f815fd30b5f9eaac02db604c7a231ed7cb2f797a"
  integrity sha1-+BX9MLX56qwC22BMeiMe18sveXo=

cli-table3@0.6.2:
  version "0.6.2"
  resolved "http://npm.htsc/cli-table3/download/cli-table3-0.6.2.tgz#aaf5df9d8b5bf12634dc8b3040806a0c07120d2a"
  integrity sha1-qvXfnYtb8SY03IswQIBqDAcSDSo=
  dependencies:
    string-width "^4.2.0"
  optionalDependencies:
    "@colors/colors" "1.5.0"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/cli-width/download/cli-width-3.0.0.tgz#a2f48437a2caa9a22436e794bf071ec9e61cedf6"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

cli-width@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.htsc/cli-width/-/cli-width-4.1.0.tgz#42daac41d3c254ef38ad8ac037672130173691c5"
  integrity sha512-ouuZd4/dm2Sw5Gmqy6bGyNNNe1qt9RpmxveLSO7KcgsTnU7RXfsw+/bukWGo1abgBiMAic068rclZsO4IWmmxQ==

cliui@^7.0.2:
  version "7.0.4"
  resolved "http://npm.htsc/cliui/download/cliui-7.0.4.tgz#a0265ee655476fc807aea9df3df8df7783808b4f"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

cliui@^8.0.1:
  version "8.0.1"
  resolved "http://npm.htsc/cliui/download/cliui-8.0.1.tgz#0c04b075db02cbfe60dc8e6cf2f5486b1a3608aa"
  integrity sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://npm.htsc/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

cls-rtracer@^2.6.0:
  version "2.6.3"
  resolved "http://registry.npm.htsc/cls-rtracer/-/cls-rtracer-2.6.3.tgz#316b39718b82b159b3c6021f6d043c7ae899a93e"
  integrity sha512-O7M/m2M/KfT9v+q7ka9nmsadS67ce9P8+1Zgm6VFamK56oFd1iCoJ9m8hYKUQpK4+RofyaexxHJlOBkxqCDs3Q==
  dependencies:
    uuid "^9.0.0"

cluster-key-slot@^1.1.0:
  version "1.1.2"
  resolved "http://npm.htsc/cluster-key-slot/download/cluster-key-slot-1.1.2.tgz#88ddaa46906e303b5de30d3153b7d9fe0a0c19ac"
  integrity sha1-iN2qRpBuMDtd4w0xU7fZ/goMGaw=

co@^4.6.0:
  version "4.6.0"
  resolved "http://npm.htsc/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

code-block-writer@^11.0.0:
  version "11.0.2"
  resolved "http://npm.htsc/code-block-writer/download/code-block-writer-11.0.2.tgz#263a1d5f982c640cda33d0704a8562057ae8b27d"
  integrity sha1-JjodX5gsZAzaM9BwSoViBXrosn0=

collect-v8-coverage@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/collect-v8-coverage/download/collect-v8-coverage-1.0.1.tgz#cc2c8e94fc18bbdffe64d6534570c8a673b27f59"
  integrity sha1-zCyOlPwYu9/+ZNZTRXDIpnOyf1k=

color-convert@^1.9.0, color-convert@^1.9.3:
  version "1.9.3"
  resolved "http://npm.htsc/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://npm.htsc/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "http://npm.htsc/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-string@^1.6.0:
  version "1.9.1"
  resolved "http://npm.htsc/color-string/download/color-string-1.9.1.tgz#4467f9146f036f855b764dfb5bf8582bf342c7a4"
  integrity sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q=
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^3.1.3:
  version "3.2.1"
  resolved "http://npm.htsc/color/download/color-3.2.1.tgz#3544dc198caf4490c3ecc9a790b54fe9ff45e164"
  integrity sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ=
  dependencies:
    color-convert "^1.9.3"
    color-string "^1.6.0"

colord@^2.9.3:
  version "2.9.3"
  resolved "http://npm.htsc/colord/download/colord-2.9.3.tgz#4f8ce919de456f1d5c1c368c307fe20f3e59fb43"
  integrity sha1-T4zpGd5Fbx1cHDaMMH/iDz5Z+0M=

colorspace@1.1.x:
  version "1.1.4"
  resolved "http://npm.htsc/colorspace/download/colorspace-1.1.4.tgz#8d442d1186152f60453bf8070cd66eb364e59243"
  integrity sha1-jUQtEYYVL2BFO/gHDNZus2TlkkM=
  dependencies:
    color "^3.1.3"
    text-hex "1.0.x"

combined-stream@^1.0.6, combined-stream@^1.0.8, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "http://npm.htsc/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@4.1.1:
  version "4.1.1"
  resolved "http://npm.htsc/commander/download/commander-4.1.1.tgz#9fd602bd936294e9e9ef46a3f4d6964044b18068"
  integrity sha1-n9YCvZNilOnp70aj9NaWQESxgGg=

commander@^2.20.0, commander@^2.20.3:
  version "2.20.3"
  resolved "http://npm.htsc/commander/download/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

component-emitter@^1.3.0:
  version "1.3.0"
  resolved "http://npm.htsc/component-emitter/download/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

compressing@1.6.2:
  version "1.6.2"
  resolved "http://npm.htsc/compressing/download/compressing-1.6.2.tgz#87320c2d867364045403792c08694e4e8284099c"
  integrity sha1-hzIMLYZzZARUA3ksCGlOToKECZw=
  dependencies:
    flushwritable "^1.0.0"
    get-ready "^1.0.0"
    iconv-lite "^0.5.0"
    mkdirp "^0.5.1"
    pump "^3.0.0"
    streamifier "^0.1.1"
    tar-stream "^1.5.2"
    yauzl "^2.7.0"
    yazl "^2.4.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://npm.htsc/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.2:
  version "1.6.2"
  resolved "http://npm.htsc/concat-stream/download/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

confusing-browser-globals@^1.0.10:
  version "1.0.11"
  resolved "http://npm.htsc/confusing-browser-globals/download/confusing-browser-globals-1.0.11.tgz#ae40e9b57cdd3915408a2805ebd3a5585608dc81"
  integrity sha1-rkDptXzdORVAiigF69OlWFYI3IE=

consola@^2.15.0:
  version "2.15.3"
  resolved "http://npm.htsc/consola/download/consola-2.15.3.tgz#2e11f98d6a4be71ff72e0bdf07bd23e12cb61550"
  integrity sha1-LhH5jWpL5x/3LgvfB70j4Sy2FVA=

content-disposition@0.5.4:
  version "0.5.4"
  resolved "http://npm.htsc/content-disposition/download/content-disposition-0.5.4.tgz#8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe"
  integrity sha1-i4K076yCUSoCuwsdzsnSxejrW/4=
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4:
  version "1.0.4"
  resolved "http://npm.htsc/content-type/download/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

content-type@~1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/content-type/download/content-type-1.0.5.tgz#8b773162656d1d1086784c8f23a54ce6d73d7918"
  integrity sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=

convert-source-map@^1.4.0, convert-source-map@^1.6.0, convert-source-map@^1.7.0:
  version "1.9.0"
  resolved "http://npm.htsc/convert-source-map/download/convert-source-map-1.9.0.tgz#7faae62353fb4213366d0ca98358d22e8368b05f"
  integrity sha1-f6rmI1P7QhM2bQypg1jSLoNosF8=

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "http://npm.htsc/cookie-signature/download/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.5.0, cookie@^0.5.0:
  version "0.5.0"
  resolved "http://npm.htsc/cookie/download/cookie-0.5.0.tgz#d1f5d71adec6558c58f389987c366aa47e994f8b"
  integrity sha1-0fXXGt7GVYxY84mYfDZqpH6ZT4s=

cookiejar@^2.1.3:
  version "2.1.3"
  resolved "http://npm.htsc/cookiejar/download/cookiejar-2.1.3.tgz#fc7a6216e408e74414b90230050842dacda75acc"
  integrity sha1-/HpiFuQI50QUuQIwBQhC2s2nWsw=

core-js-compat@^3.25.1:
  version "3.25.5"
  resolved "http://npm.htsc/core-js-compat/download/core-js-compat-3.25.5.tgz#0016e8158c904f7b059486639e6e82116eafa7d9"
  integrity sha1-ABboFYyQT3sFlIZjnm6CEW6vp9k=
  dependencies:
    browserslist "^4.21.4"

core-js-pure@^3.25.1:
  version "3.25.5"
  resolved "http://npm.htsc/core-js-pure/download/core-js-pure-3.25.5.tgz#79716ba54240c6aa9ceba6eee08cf79471ba184d"
  integrity sha1-eXFrpUJAxqqc66bu4Iz3lHG6GE0=

core-js@^3.25.0:
  version "3.26.0"
  resolved "http://npm.htsc/core-js/download/core-js-3.26.0.tgz#a516db0ed0811be10eac5d94f3b8463d03faccfe"
  integrity sha1-pRbbDtCBG+EOrF2U87hGPQP6zP4=

core-util-is@1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

core-util-is@^1.0.2, core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://npm.htsc/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cors@2.8.5, cors@^2.8.5:
  version "2.8.5"
  resolved "http://npm.htsc/cors/download/cors-2.8.5.tgz#eac11da51592dd86b9f06f6e7ac293b3df875d29"
  integrity sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=
  dependencies:
    object-assign "^4"
    vary "^1"

cosmiconfig@^7.0.1:
  version "7.0.1"
  resolved "http://npm.htsc/cosmiconfig/download/cosmiconfig-7.0.1.tgz#714d756522cace867867ccb4474c5d01bbae5d6d"
  integrity sha1-cU11ZSLKzoZ4Z8y0R0xdAbuuXW0=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cosmiconfig@^7.1.0:
  version "7.1.0"
  resolved "http://npm.htsc/cosmiconfig/download/cosmiconfig-7.1.0.tgz#1443b9afa596b670082ea46cbd8f6a62b84635f6"
  integrity sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

create-require@^1.1.0:
  version "1.1.1"
  resolved "http://npm.htsc/create-require/download/create-require-1.1.1.tgz#c1d7e8f1e5f6cfc9ff65f9cd352d37348756c333"
  integrity sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=

cross-spawn@^7.0.0, cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "http://npm.htsc/cross-spawn/download/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-functions-list@^3.1.0:
  version "3.1.0"
  resolved "http://npm.htsc/css-functions-list/download/css-functions-list-3.1.0.tgz#cf5b09f835ad91a00e5959bcfc627cd498e1321b"
  integrity sha1-z1sJ+DWtkaAOWVm8/GJ81JjhMhs=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/cssesc/download/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssfilter@0.0.10:
  version "0.0.10"
  resolved "http://npm.htsc/cssfilter/download/cssfilter-0.0.10.tgz#c6d2672632a2e5c83e013e6864a42ce8defd20ae"
  integrity sha1-xtJnJjKi5cg+AT5oZKQs6N79IK4=

ctrip-apollo@^4.5.0:
  version "4.5.0"
  resolved "http://npm.htsc/ctrip-apollo/download/ctrip-apollo-4.5.0.tgz#74dff7eda977f606a3fb7c31081d7d3611c26248"
  integrity sha1-dN/37al39gaj+3wxCB19NhHCYkg=
  dependencies:
    core-util-is "^1.0.2"
    diff-sorted-array "^3.0.0"
    err-object "^5.1.4"
    fs-extra "^8.1.0"
    request "^2.88.0"

damerau-levenshtein@^1.0.8:
  version "1.0.8"
  resolved "http://npm.htsc/damerau-levenshtein/download/damerau-levenshtein-1.0.8.tgz#b43d286ccbd36bc5b2f7ed41caf2d0aba1f8a6e7"
  integrity sha1-tD0obMvTa8Wy9+1ByvLQq6H4puc=

dashdash@^1.12.0:
  version "1.14.1"
  resolved "http://npm.htsc/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

date-fns@^2.28.0:
  version "2.29.3"
  resolved "http://npm.htsc/date-fns/download/date-fns-2.29.3.tgz#27402d2fc67eb442b511b70bbdf98e6411cd68a8"
  integrity sha1-J0AtL8Z+tEK1EbcLvfmOZBHNaKg=

dayjs@1.11.6:
  version "1.11.6"
  resolved "http://npm.htsc/dayjs/download/dayjs-1.11.6.tgz#2e79a226314ec3ec904e3ee1dd5a4f5e5b1c7afb"
  integrity sha1-LnmiJjFOw+yQTj7h3VpPXlscevs=

debug@2.6.9, debug@^2.6.9:
  version "2.6.9"
  resolved "http://npm.htsc/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.2.7:
  version "3.2.7"
  resolved "http://npm.htsc/debug/download/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.0.0, debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.2, debug@^4.3.3, debug@^4.3.4:
  version "4.3.4"
  resolved "http://npm.htsc/debug/download/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=
  dependencies:
    ms "2.1.2"

decamelize-keys@^1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/decamelize-keys/download/decamelize-keys-1.1.0.tgz#d171a87933252807eb3cb61dc1c1445d078df2d9"
  integrity sha1-0XGoeTMlKAfrPLYdwcFEXQeN8tk=
  dependencies:
    decamelize "^1.1.0"
    map-obj "^1.0.0"

decamelize@^1.1.0, decamelize@^1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

dedent@^0.7.0:
  version "0.7.0"
  resolved "http://npm.htsc/dedent/download/dedent-0.7.0.tgz#2495ddbaf6eb874abb0e1be9df22d2e5a544326c"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-is@^0.1.3:
  version "0.1.4"
  resolved "http://npm.htsc/deep-is/download/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^4.2.2:
  version "4.2.2"
  resolved "http://npm.htsc/deepmerge/download/deepmerge-4.2.2.tgz#44d2ea3679b8f4d4ffba33f03d865fc1e7bf4955"
  integrity sha1-RNLqNnm49NT/ujPwPYZfwee/SVU=

defaults@^1.0.3:
  version "1.0.4"
  resolved "http://npm.htsc/defaults/download/defaults-1.0.4.tgz#b0b02062c1e2aa62ff5d9528f0f98baa90978d7a"
  integrity sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=
  dependencies:
    clone "^1.0.2"

define-data-property@^1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.htsc/define-data-property/-/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.1.4:
  version "1.1.4"
  resolved "http://npm.htsc/define-properties/download/define-properties-1.1.4.tgz#0b14d7bd7fbeb2f3572c3a7eda80ea5d57fb05b1"
  integrity sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=
  dependencies:
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

denque@^2.0.1, denque@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/denque/download/denque-2.1.0.tgz#e93e1a6569fb5e66f16a3c2a2964617d349d6ab1"
  integrity sha1-6T4aZWn7XmbxajwqKWRhfTSdarE=

depd@2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/depd/download/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

destroy@1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/destroy/download/destroy-1.2.0.tgz#4803735509ad8be552934c67df614f94e66fa015"
  integrity sha1-SANzVQmti+VSk0xn32FPlOZvoBU=

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "http://npm.htsc/detect-newline/download/detect-newline-3.1.0.tgz#576f5dfc63ae1a192ff192d8ad3af6308991b651"
  integrity sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=

dezalgo@1.0.3:
  version "1.0.3"
  resolved "http://npm.htsc/dezalgo/download/dezalgo-1.0.3.tgz#7f742de066fc748bc8db820569dddce49bf0d456"
  integrity sha1-f3Qt4Gb8dIvI24IFad3c5Jvw1FY=
  dependencies:
    asap "^2.0.0"
    wrappy "1"

diff-sequences@^28.1.1:
  version "28.1.1"
  resolved "http://npm.htsc/diff-sequences/download/diff-sequences-28.1.1.tgz#9989dc731266dc2903457a70e996f3a041913ac6"
  integrity sha1-mYnccxJm3CkDRXpw6ZbzoEGROsY=

diff-sorted-array@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/diff-sorted-array/download/diff-sorted-array-3.0.0.tgz#7042c3fe5c33df97e02c1cd33d9f859c4c8ccf6e"
  integrity sha1-cELD/lwz35fgLBzTPZ+FnEyMz24=

diff@^4.0.1:
  version "4.0.2"
  resolved "http://npm.htsc/diff/download/diff-4.0.2.tgz#60f3aecb89d5fae520c11aa19efc2bb982aade7d"
  integrity sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://npm.htsc/dir-glob/download/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/doctrine/download/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-serializer@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/dom-serializer/download/dom-serializer-2.0.0.tgz#e41b802e1eedf9f6cae183ce5e622d789d7d8e53"
  integrity sha1-5BuALh7t+fbK4YPOXmIteJ19jlM=
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

domelementtype@^2.3.0:
  version "2.3.0"
  resolved "http://npm.htsc/domelementtype/download/domelementtype-2.3.0.tgz#5c45e8e869952626331d7aab326d01daf65d589d"
  integrity sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=

domhandler@^5.0.1, domhandler@^5.0.2:
  version "5.0.3"
  resolved "http://npm.htsc/domhandler/download/domhandler-5.0.3.tgz#cc385f7f751f1d1fc650c21374804254538c7d31"
  integrity sha1-zDhff3UfHR/GUMITdIBCVFOMfTE=
  dependencies:
    domelementtype "^2.3.0"

domutils@^3.0.1:
  version "3.0.1"
  resolved "http://npm.htsc/domutils/download/domutils-3.0.1.tgz#696b3875238338cb186b6c0612bd4901c89a4f1c"
  integrity sha1-aWs4dSODOMsYa2wGEr1JAciaTxw=
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.1"

dotenv-expand@8.0.3:
  version "8.0.3"
  resolved "http://npm.htsc/dotenv-expand/download/dotenv-expand-8.0.3.tgz#29016757455bcc748469c83a19b36aaf2b83dd6e"
  integrity sha1-KQFnV0VbzHSEacg6GbNqryuD3W4=

dotenv@16.0.1:
  version "16.0.1"
  resolved "http://npm.htsc/dotenv/download/dotenv-16.0.1.tgz#8f8f9d94876c35dac989876a5d3a82a267fdce1d"
  integrity sha1-j4+dlIdsNdrJiYdqXTqComf9zh0=

dotenv@^16.0.0:
  version "16.0.3"
  resolved "http://npm.htsc/dotenv/download/dotenv-16.0.3.tgz#115aec42bac5053db3c456db30cc243a5a836a07"
  integrity sha1-EVrsQrrFBT2zxFbbMMwkOlqDagc=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "http://npm.htsc/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ecdsa-sig-formatter@1.0.11:
  version "1.0.11"
  resolved "http://npm.htsc/ecdsa-sig-formatter/download/ecdsa-sig-formatter-1.0.11.tgz#ae0f0fa2d85045ef14a817daa3ce9acd0489e5bf"
  integrity sha1-rg8PothQRe8UqBfao86azQSJ5b8=
  dependencies:
    safe-buffer "^5.0.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@3.1.8:
  version "3.1.8"
  resolved "http://npm.htsc/ejs/download/ejs-3.1.8.tgz#758d32910c78047585c7ef1f92f9ee041c1c190b"
  integrity sha1-dY0ykQx4BHWFx+8fkvnuBBwcGQs=
  dependencies:
    jake "^10.8.5"

electron-to-chromium@^1.4.251:
  version "1.4.283"
  resolved "http://npm.htsc/electron-to-chromium/download/electron-to-chromium-1.4.283.tgz#d4f263f5df402fd799c0a06255d580dcf8aa9a8e"
  integrity sha1-1PJj9d9AL9eZwKBiVdWA3Piqmo4=

emittery@^0.10.2:
  version "0.10.2"
  resolved "http://npm.htsc/emittery/download/emittery-0.10.2.tgz#902eec8aedb8c41938c46e9385e9db7e03182933"
  integrity sha1-kC7siu24xBk4xG6ThenbfgMYKTM=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://npm.htsc/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "http://npm.htsc/emoji-regex/download/emoji-regex-9.2.2.tgz#840c8803b0d8047f4ff0cf963176b32d4ef3ed72"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

enabled@2.0.x:
  version "2.0.0"
  resolved "http://npm.htsc/enabled/download/enabled-2.0.0.tgz#f9dd92ec2d6f4bbc0d5d1e64e21d61cd4665e7c2"
  integrity sha1-+d2S7C1vS7wNXR5k4h1hzUZl58I=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "http://npm.htsc/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enhanced-resolve@^0.9.1:
  version "0.9.1"
  resolved "http://npm.htsc/enhanced-resolve/download/enhanced-resolve-0.9.1.tgz#4d6e689b3725f86090927ccc86cd9f1635b89e2e"
  integrity sha1-TW5omzcl+GCQknzMhs2fFjW4ni4=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.2.0"
    tapable "^0.1.8"

enhanced-resolve@^5.0.0, enhanced-resolve@^5.7.0, enhanced-resolve@^5.9.3:
  version "5.10.0"
  resolved "http://npm.htsc/enhanced-resolve/download/enhanced-resolve-5.10.0.tgz#0dc579c3bb2a1032e357ac45b8f3a6f3ad4fb1e6"
  integrity sha1-DcV5w7sqEDLjV6xFuPOm861PseY=
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

enquirer@^2.3.5:
  version "2.3.6"
  resolved "http://npm.htsc/enquirer/download/enquirer-2.3.6.tgz#2a7fe5dd634a1e4125a975ec994ff5456dc3734d"
  integrity sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00=
  dependencies:
    ansi-colors "^4.1.1"

entities@^4.2.0, entities@^4.3.0:
  version "4.4.0"
  resolved "http://npm.htsc/entities/download/entities-4.4.0.tgz#97bdaba170339446495e653cfd2db78962900174"
  integrity sha1-l72roXAzlEZJXmU8/S23iWKQAXQ=

err-object@^5.1.4:
  version "5.1.4"
  resolved "http://npm.htsc/err-object/download/err-object-5.1.4.tgz#35c31edc3823853658797225407b527848caa80d"
  integrity sha1-NcMe3DgjhTZYeXIlQHtSeEjKqA0=
  dependencies:
    error-stack "^1.0.2"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://npm.htsc/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack@^1.0.2:
  version "1.0.3"
  resolved "http://npm.htsc/error-stack/download/error-stack-1.0.3.tgz#b4799bc3381cef0ab88fac6d417a5a750fcfd804"
  integrity sha1-tHmbwzgc7wq4j6xtQXpadQ/P2AQ=

es-abstract@^1.19.0, es-abstract@^1.19.1, es-abstract@^1.19.2, es-abstract@^1.19.5:
  version "1.20.4"
  resolved "http://npm.htsc/es-abstract/download/es-abstract-1.20.4.tgz#1d103f9f8d78d4cf0713edcd6d0ed1a46eed5861"
  integrity sha1-HRA/n4141M8HE+3NbQ7RpG7tWGE=
  dependencies:
    call-bind "^1.0.2"
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    function.prototype.name "^1.1.5"
    get-intrinsic "^1.1.3"
    get-symbol-description "^1.0.0"
    has "^1.0.3"
    has-property-descriptors "^1.0.0"
    has-symbols "^1.0.3"
    internal-slot "^1.0.3"
    is-callable "^1.2.7"
    is-negative-zero "^2.0.2"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.2"
    is-string "^1.0.7"
    is-weakref "^1.0.2"
    object-inspect "^1.12.2"
    object-keys "^1.1.1"
    object.assign "^4.1.4"
    regexp.prototype.flags "^1.4.3"
    safe-regex-test "^1.0.0"
    string.prototype.trimend "^1.0.5"
    string.prototype.trimstart "^1.0.5"
    unbox-primitive "^1.0.2"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.htsc/es-define-property/-/es-define-property-1.0.0.tgz#c7faefbdff8b2696cf5f46921edfb77cc4ba3845"
  integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.htsc/es-errors/-/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-module-lexer@^0.9.0:
  version "0.9.3"
  resolved "http://npm.htsc/es-module-lexer/download/es-module-lexer-0.9.3.tgz#6f13db00cc38417137daf74366f535c8eb438f19"
  integrity sha1-bxPbAMw4QXE32vdDZvU1yOtDjxk=

es-shim-unscopables@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/es-shim-unscopables/download/es-shim-unscopables-1.0.0.tgz#702e632193201e3edf8713635d083d378e510241"
  integrity sha1-cC5jIZMgHj7fhxNjXQg9N45RAkE=
  dependencies:
    has "^1.0.3"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "http://npm.htsc/es-to-primitive/download/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "http://npm.htsc/escalade/download/escalade-3.1.1.tgz#d8cfdc7000965c5a0174b4a82eaa5c0552742e40"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "http://npm.htsc/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz#a30304e99daa32e23b2fd20f51babd07cffca344"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-config-airbnb-base@^14.2.1:
  version "14.2.1"
  resolved "http://npm.htsc/eslint-config-airbnb-base/download/eslint-config-airbnb-base-14.2.1.tgz#8a2eb38455dc5a312550193b319cdaeef042cd1e"
  integrity sha1-ii6zhFXcWjElUBk7MZza7vBCzR4=
  dependencies:
    confusing-browser-globals "^1.0.10"
    object.assign "^4.1.2"
    object.entries "^1.1.2"

eslint-config-airbnb@^18.2.1:
  version "18.2.1"
  resolved "http://npm.htsc/eslint-config-airbnb/download/eslint-config-airbnb-18.2.1.tgz#b7fe2b42f9f8173e825b73c8014b592e449c98d9"
  integrity sha1-t/4rQvn4Fz6CW3PIAUtZLkScmNk=
  dependencies:
    eslint-config-airbnb-base "^14.2.1"
    object.assign "^4.1.2"
    object.entries "^1.1.2"

eslint-config-prettier@^8.3.0:
  version "8.5.0"
  resolved "http://npm.htsc/eslint-config-prettier/download/eslint-config-prettier-8.5.0.tgz#5a81680ec934beca02c7b1a61cf8ca34b66feab1"
  integrity sha1-WoFoDsk0vsoCx7GmHPjKNLZv6rE=

eslint-import-resolver-node@^0.3.6:
  version "0.3.6"
  resolved "http://npm.htsc/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.6.tgz#4048b958395da89668252001dbd9eca6b83bacbd"
  integrity sha1-QEi5WDldqJZoJSAB29nsprg7rL0=
  dependencies:
    debug "^3.2.7"
    resolve "^1.20.0"

eslint-import-resolver-webpack@^0.13.1:
  version "0.13.2"
  resolved "http://npm.htsc/eslint-import-resolver-webpack/download/eslint-import-resolver-webpack-0.13.2.tgz#fc813df0d08b9265cc7072d22393bda5198bdc1e"
  integrity sha1-/IE98NCLkmXMcHLSI5O9pRmL3B4=
  dependencies:
    array-find "^1.0.0"
    debug "^3.2.7"
    enhanced-resolve "^0.9.1"
    find-root "^1.1.0"
    has "^1.0.3"
    interpret "^1.4.0"
    is-core-module "^2.7.0"
    is-regex "^1.1.4"
    lodash "^4.17.21"
    resolve "^1.20.0"
    semver "^5.7.1"

eslint-module-utils@^2.7.3:
  version "2.7.4"
  resolved "http://npm.htsc/eslint-module-utils/download/eslint-module-utils-2.7.4.tgz#4f3e41116aaf13a20792261e61d3a2e7e0583974"
  integrity sha1-Tz5BEWqvE6IHkiYeYdOi5+BYOXQ=
  dependencies:
    debug "^3.2.7"

eslint-plugin-es@^3.0.0:
  version "3.0.1"
  resolved "http://npm.htsc/eslint-plugin-es/download/eslint-plugin-es-3.0.1.tgz#75a7cdfdccddc0589934aeeb384175f221c57893"
  integrity sha1-dafN/czdwFiZNK7rOEF18iHFeJM=
  dependencies:
    eslint-utils "^2.0.0"
    regexpp "^3.0.0"

eslint-plugin-eslint-comments@^3.1.1:
  version "3.2.0"
  resolved "http://npm.htsc/eslint-plugin-eslint-comments/download/eslint-plugin-eslint-comments-3.2.0.tgz#9e1cd7b4413526abb313933071d7aba05ca12ffa"
  integrity sha1-nhzXtEE1JquzE5MwcderoFyhL/o=
  dependencies:
    escape-string-regexp "^1.0.5"
    ignore "^5.0.5"

eslint-plugin-filenames@^1.3.2:
  version "1.3.2"
  resolved "http://npm.htsc/eslint-plugin-filenames/download/eslint-plugin-filenames-1.3.2.tgz#7094f00d7aefdd6999e3ac19f72cea058e590cf7"
  integrity sha1-cJTwDXrv3WmZ46wZ9yzqBY5ZDPc=
  dependencies:
    lodash.camelcase "4.3.0"
    lodash.kebabcase "4.1.1"
    lodash.snakecase "4.1.1"
    lodash.upperfirst "4.3.1"

eslint-plugin-import@^2.18.2:
  version "2.26.0"
  resolved "http://npm.htsc/eslint-plugin-import/download/eslint-plugin-import-2.26.0.tgz#f812dc47be4f2b72b478a021605a59fc6fe8b88b"
  integrity sha1-+BLcR75PK3K0eKAhYFpZ/G/ouIs=
  dependencies:
    array-includes "^3.1.4"
    array.prototype.flat "^1.2.5"
    debug "^2.6.9"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.6"
    eslint-module-utils "^2.7.3"
    has "^1.0.3"
    is-core-module "^2.8.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.values "^1.1.5"
    resolve "^1.22.0"
    tsconfig-paths "^3.14.1"

eslint-plugin-jsx-a11y@^6.4.1:
  version "6.6.1"
  resolved "http://npm.htsc/eslint-plugin-jsx-a11y/download/eslint-plugin-jsx-a11y-6.6.1.tgz#93736fc91b83fdc38cc8d115deedfc3091aef1ff"
  integrity sha1-k3NvyRuD/cOMyNEV3u38MJGu8f8=
  dependencies:
    "@babel/runtime" "^7.18.9"
    aria-query "^4.2.2"
    array-includes "^3.1.5"
    ast-types-flow "^0.0.7"
    axe-core "^4.4.3"
    axobject-query "^2.2.0"
    damerau-levenshtein "^1.0.8"
    emoji-regex "^9.2.2"
    has "^1.0.3"
    jsx-ast-utils "^3.3.2"
    language-tags "^1.0.5"
    minimatch "^3.1.2"
    semver "^6.3.0"

eslint-plugin-markdown@^2.2.0:
  version "2.2.1"
  resolved "http://npm.htsc/eslint-plugin-markdown/download/eslint-plugin-markdown-2.2.1.tgz#76b8a970099fbffc6cc1ffcad9772b96911c027a"
  integrity sha1-dripcAmfv/xswf/K2XcrlpEcAno=
  dependencies:
    mdast-util-from-markdown "^0.8.5"

eslint-plugin-node@^11.1.0:
  version "11.1.0"
  resolved "http://npm.htsc/eslint-plugin-node/download/eslint-plugin-node-11.1.0.tgz#c95544416ee4ada26740a30474eefc5402dc671d"
  integrity sha1-yVVEQW7kraJnQKMEdO78VALcZx0=
  dependencies:
    eslint-plugin-es "^3.0.0"
    eslint-utils "^2.0.0"
    ignore "^5.1.1"
    minimatch "^3.0.4"
    resolve "^1.10.1"
    semver "^6.1.0"

eslint-plugin-prettier@^3.4.1:
  version "3.4.1"
  resolved "http://npm.htsc/eslint-plugin-prettier/download/eslint-plugin-prettier-3.4.1.tgz#e9ddb200efb6f3d05ffe83b1665a716af4a387e5"
  integrity sha1-6d2yAO+289Bf/oOxZlpxavSjh+U=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-promise@^5.1.0:
  version "5.2.0"
  resolved "http://npm.htsc/eslint-plugin-promise/download/eslint-plugin-promise-5.2.0.tgz#a596acc32981627eb36d9d75f9666ac1a4564971"
  integrity sha1-pZaswymBYn6zbZ11+WZqwaRWSXE=

eslint-plugin-react-hooks@^4.2.0:
  version "4.6.0"
  resolved "http://npm.htsc/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-4.6.0.tgz#4c3e697ad95b77e93f8646aaa1630c1ba607edd3"
  integrity sha1-TD5petlbd+k/hkaqoWMMG6YH7dM=

eslint-plugin-react@^7.24.0:
  version "7.31.10"
  resolved "http://npm.htsc/eslint-plugin-react/download/eslint-plugin-react-7.31.10.tgz#6782c2c7fe91c09e715d536067644bbb9491419a"
  integrity sha1-Z4LCx/6RwJ5xXVNgZ2RLu5SRQZo=
  dependencies:
    array-includes "^3.1.5"
    array.prototype.flatmap "^1.3.0"
    doctrine "^2.1.0"
    estraverse "^5.3.0"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.5"
    object.fromentries "^2.0.5"
    object.hasown "^1.1.1"
    object.values "^1.1.5"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.3"
    semver "^6.3.0"
    string.prototype.matchall "^4.0.7"

eslint-plugin-vue@8.7.1:
  version "8.7.1"
  resolved "http://npm.htsc/eslint-plugin-vue/download/eslint-plugin-vue-8.7.1.tgz#f13c53547a0c9d64588a675cc5ecc6ccaf63703f"
  integrity sha1-8TxTVHoMnWRYimdcxezGzK9jcD8=
  dependencies:
    eslint-utils "^3.0.0"
    natural-compare "^1.4.0"
    nth-check "^2.0.1"
    postcss-selector-parser "^6.0.9"
    semver "^7.3.5"
    vue-eslint-parser "^8.0.1"

eslint-rule-composer@^0.3.0:
  version "0.3.0"
  resolved "http://npm.htsc/eslint-rule-composer/download/eslint-rule-composer-0.3.0.tgz#79320c927b0c5c0d3d3d2b76c8b4a488f25bbaf9"
  integrity sha1-eTIMknsMXA09PSt2yLSkiPJbuvk=

eslint-scope@5.1.1, eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "http://npm.htsc/eslint-scope/download/eslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.0.0:
  version "7.1.1"
  resolved "http://npm.htsc/eslint-scope/download/eslint-scope-7.1.1.tgz#fff34894c2f65e5226d3041ac480b4513a163642"
  integrity sha1-//NIlML2XlIm0wQaxIC0UToWNkI=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-utils@^2.0.0, eslint-utils@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/eslint-utils/download/eslint-utils-2.1.0.tgz#d2de5e03424e707dc10c74068ddedae708741b27"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/eslint-utils/download/eslint-utils-3.0.0.tgz#8aebaface7345bb33559db0a1f13a1d2d48c3672"
  integrity sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^1.1.0, eslint-visitor-keys@^1.3.0:
  version "1.3.0"
  resolved "http://npm.htsc/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz#30ebd1ef7c2fdff01c3a4f151044af25fab0523e"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint-visitor-keys@^2.0.0, eslint-visitor-keys@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz#f65328259305927392c938ed44eb0a5c9b2bd303"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint-visitor-keys@^3.1.0, eslint-visitor-keys@^3.3.0:
  version "3.3.0"
  resolved "http://npm.htsc/eslint-visitor-keys/download/eslint-visitor-keys-3.3.0.tgz#f6480fa6b1f30efe2d1968aa8ac745b862469826"
  integrity sha1-9kgPprHzDv4tGWiqisdFuGJGmCY=

eslint@^7.32.0:
  version "7.32.0"
  resolved "http://npm.htsc/eslint/download/eslint-7.32.0.tgz#c6d328a14be3fb08c8d1d21e12c02fdb7a2a812d"
  integrity sha1-xtMooUvj+wjI0dIeEsAv23oqgS0=
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.0.1"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    escape-string-regexp "^4.0.0"
    eslint-scope "^5.1.1"
    eslint-utils "^2.1.0"
    eslint-visitor-keys "^2.0.0"
    espree "^7.3.1"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.1.2"
    globals "^13.6.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.1.0"
    semver "^7.2.1"
    strip-ansi "^6.0.0"
    strip-json-comments "^3.1.0"
    table "^6.0.9"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^7.3.0, espree@^7.3.1:
  version "7.3.1"
  resolved "http://npm.htsc/espree/download/espree-7.3.1.tgz#f2df330b752c6f55019f8bd89b7660039c1bbbb6"
  integrity sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=
  dependencies:
    acorn "^7.4.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^1.3.0"

espree@^9.0.0:
  version "9.4.0"
  resolved "http://npm.htsc/espree/download/espree-9.4.0.tgz#cd4bc3d6e9336c433265fc0aa016fc1aaf182f8a"
  integrity sha1-zUvD1ukzbEMyZfwKoBb8Gq8YL4o=
  dependencies:
    acorn "^8.8.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.3.0"

esprima@^4.0.0:
  version "4.0.1"
  resolved "http://npm.htsc/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.0:
  version "1.4.0"
  resolved "http://npm.htsc/esquery/download/esquery-1.4.0.tgz#2148ffc38b82e8c7057dfed48425b3e61f0f24a5"
  integrity sha1-IUj/w4uC6McFff7UhCWz5h8PJKU=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://npm.htsc/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "http://npm.htsc/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "http://npm.htsc/estraverse/download/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://npm.htsc/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "http://npm.htsc/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

eventemitter3@^3.1.0:
  version "3.1.2"
  resolved "http://npm.htsc/eventemitter3/download/eventemitter3-3.1.2.tgz#2d3d48f9c346698fce83a85d7d664e98535df6e7"
  integrity sha1-LT1I+cNGaY/Og6hdfWZOmFNd9uc=

events@^3.2.0:
  version "3.3.0"
  resolved "http://npm.htsc/events/download/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

execa@^4.0.2:
  version "4.1.0"
  resolved "http://npm.htsc/execa/download/execa-4.1.0.tgz#4e5491ad1572f2f17a77d388c6c857135b22847a"
  integrity sha1-TlSRrRVy8vF6d9OIxshXE1sihHo=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

execa@^5.0.0:
  version "5.1.1"
  resolved "http://npm.htsc/execa/download/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "http://npm.htsc/exit/download/exit-0.1.2.tgz#0632638f8d877cc82107d30a0fff1a17cba1cd0c"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expect@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/expect/download/expect-28.1.3.tgz#90a7c1a124f1824133dd4533cce2d2bdcb6603ec"
  integrity sha1-kKfBoSTxgkEz3UUzzOLSvctmA+w=
  dependencies:
    "@jest/expect-utils" "^28.1.3"
    jest-get-type "^28.0.2"
    jest-matcher-utils "^28.1.3"
    jest-message-util "^28.1.3"
    jest-util "^28.1.3"

express@4.18.1:
  version "4.18.1"
  resolved "http://npm.htsc/express/download/express-4.18.1.tgz#7797de8b9c72c857b9cd0e14a5eea80666267caf"
  integrity sha1-d5fei5xyyFe5zQ4Upe6oBmYmfK8=
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.0"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.5.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.10.3"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

express@4.18.2, express@^4.17.1:
  version "4.18.2"
  resolved "http://npm.htsc/express/download/express-4.18.2.tgz#3fabe08296e930c796c19e3c516979386ba9fd59"
  integrity sha1-P6vggpbpMMeWwZ48UWl5OGup/Vk=
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.1"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.5.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.11.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend@~3.0.2:
  version "3.0.2"
  resolved "http://npm.htsc/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3, external-editor@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.htsc/external-editor/-/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "http://npm.htsc/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.1"
  resolved "http://npm.htsc/extsprintf/download/extsprintf-1.4.1.tgz#8d172c064867f235c0c84a596806d279bf4bcc07"
  integrity sha1-jRcsBkhn8jXAyEpZaAbSeb9LzAc=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://npm.htsc/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.2.0"
  resolved "http://npm.htsc/fast-diff/download/fast-diff-1.2.0.tgz#73ee11982d86caaf7959828d519cfe927fac5f03"
  integrity sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM=

fast-glob@3.2.12, fast-glob@^3.2.11, fast-glob@^3.2.12, fast-glob@^3.2.9:
  version "3.2.12"
  resolved "http://npm.htsc/fast-glob/download/fast-glob-3.2.12.tgz#7f39ec99c2e6ab030337142da9e0c18f37afae80"
  integrity sha1-fznsmcLmqwMDNxQtqeDBjzevroA=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@2.x, fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://npm.htsc/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "http://npm.htsc/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-safe-stringify@2.1.1, fast-safe-stringify@^2.1.1:
  version "2.1.1"
  resolved "http://npm.htsc/fast-safe-stringify/download/fast-safe-stringify-2.1.1.tgz#c406a83b6e70d9e35ce3b30a81141df30aeba884"
  integrity sha1-xAaoO25w2eNc47MKgRQd8wrrqIQ=

fastest-levenshtein@^1.0.16:
  version "1.0.16"
  resolved "http://npm.htsc/fastest-levenshtein/download/fastest-levenshtein-1.0.16.tgz#210e61b6ff181de91ea9b3d1b84fdedd47e034e5"
  integrity sha1-IQ5htv8YHekeqbPRuE/e3UfgNOU=

fastq@^1.6.0:
  version "1.13.0"
  resolved "http://npm.htsc/fastq/download/fastq-1.13.0.tgz#616760f88a7526bdfc596b7cab8c18938c36b98c"
  integrity sha1-YWdg+Ip1Jr38WWt8q4wYk4w2uYw=
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.0:
  version "2.0.2"
  resolved "http://npm.htsc/fb-watchman/download/fb-watchman-2.0.2.tgz#e9524ee6b5c77e9e5001af0f85f3adbb8623255c"
  integrity sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=
  dependencies:
    bser "2.1.1"

fd-slicer@~1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/fd-slicer/download/fd-slicer-1.1.0.tgz#25c7c89cb1f9077f8891bbe61d8f390eae256f1e"
  integrity sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=
  dependencies:
    pend "~1.2.0"

fecha@^4.2.0:
  version "4.2.3"
  resolved "http://npm.htsc/fecha/download/fecha-4.2.3.tgz#4d9ccdbc61e8629b259fdca67e65891448d569fd"
  integrity sha1-TZzNvGHoYpsln9ymfmWJFEjVaf0=

figures@^3.0.0, figures@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.htsc/figures/-/figures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://npm.htsc/file-entry-cache/download/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

filelist@^1.0.1:
  version "1.0.3"
  resolved "http://npm.htsc/filelist/download/filelist-1.0.3.tgz#448607750376484932f67ef1b9ff07386b036c83"
  integrity sha1-RIYHdQN2SEky9n7xuf8HOGsDbIM=
  dependencies:
    minimatch "^5.0.1"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "http://npm.htsc/fill-range/download/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/finalhandler/download/finalhandler-1.2.0.tgz#7d23fe5731b207b4640e4fcd00aec1f9207a7b32"
  integrity sha1-fSP+VzGyB7RkDk/NAK7B+SB6ezI=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-root@^1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/find-root/download/find-root-1.1.0.tgz#abcfc8ba76f708c42a97b3d685b7e9450bfb9ce4"
  integrity sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ=

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.0.4"
  resolved "http://npm.htsc/flat-cache/download/flat-cache-3.0.4.tgz#61b0338302b2fe9f957dcc32fc2a87f1c3048b11"
  integrity sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.2.7"
  resolved "http://npm.htsc/flatted/download/flatted-3.2.7.tgz#609f39207cb614b89d0765b477cb2d437fbf9787"
  integrity sha1-YJ85IHy2FLidB2W0d8stQ3+/l4c=

flushwritable@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/flushwritable/download/flushwritable-1.0.0.tgz#3e328d8fde412ad47e738e3be750b4d290043498"
  integrity sha1-PjKNj95BKtR+c44751C00pAENJg=

fn.name@1.x.x:
  version "1.1.0"
  resolved "http://npm.htsc/fn.name/download/fn.name-1.1.0.tgz#26cad8017967aea8731bc42961d04a3d5988accc"
  integrity sha1-JsrYAXlnrqhzG8QpYdBKPVmIrMw=

follow-redirects@^1.15.0, follow-redirects@^1.15.6:
  version "1.15.9"
  resolved "http://registry.npm.htsc/follow-redirects/-/follow-redirects-1.15.9.tgz#a604fa10e443bf98ca94228d9eebcc2e8a2c8ee1"
  integrity sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==

for-each@^0.3.3:
  version "0.3.3"
  resolved "http://npm.htsc/for-each/download/for-each-0.3.3.tgz#69b447e88a0a5d32c3e7084f3f1710034b21376e"
  integrity sha1-abRH6IoKXTLD5whPPxcQA0shN24=
  dependencies:
    is-callable "^1.1.3"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "http://npm.htsc/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

fork-ts-checker-webpack-plugin@7.2.11:
  version "7.2.11"
  resolved "http://npm.htsc/fork-ts-checker-webpack-plugin/download/fork-ts-checker-webpack-plugin-7.2.11.tgz#aff3febbc11544ba3ad0ae4d5aa4055bd15cd26d"
  integrity sha1-r/P+u8EVRLo60K5NWqQFW9Fc0m0=
  dependencies:
    "@babel/code-frame" "^7.16.7"
    chalk "^4.1.2"
    chokidar "^3.5.3"
    cosmiconfig "^7.0.1"
    deepmerge "^4.2.2"
    fs-extra "^10.0.0"
    memfs "^3.4.1"
    minimatch "^3.0.4"
    schema-utils "^3.1.1"
    semver "^7.3.5"
    tapable "^2.2.1"

form-data@^3.0.0:
  version "3.0.1"
  resolved "http://npm.htsc/form-data/download/form-data-3.0.1.tgz#ebd53791b78356a99af9a300d4282c4d5eb9755f"
  integrity sha1-69U3kbeDVqma+aMA1CgsTV65dV8=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

form-data@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/form-data/download/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha1-k5Gdrq82HuUpWEubMWZNwSyfpFI=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

form-data@~2.3.2:
  version "2.3.3"
  resolved "http://npm.htsc/form-data/download/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

formidable@^2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/formidable/download/formidable-2.0.1.tgz#4310bc7965d185536f9565184dee74fbb75557ff"
  integrity sha1-QxC8eWXRhVNvlWUYTe50+7dVV/8=
  dependencies:
    dezalgo "1.0.3"
    hexoid "1.0.0"
    once "1.4.0"
    qs "6.9.3"

forwarded@0.2.0:
  version "0.2.0"
  resolved "http://npm.htsc/forwarded/download/forwarded-0.2.0.tgz#2269936428aad4c15c7ebe9779a84bf0b2a81811"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fresh@0.5.2:
  version "0.5.2"
  resolved "http://npm.htsc/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-constants@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/fs-constants/download/fs-constants-1.0.0.tgz#6be0de9be998ce16af8afc24497b9ee9b7ccd9ad"
  integrity sha1-a+Dem+mYzhavivwkSXue6bfM2a0=

fs-extra@10.1.0, fs-extra@^10.0.0:
  version "10.1.0"
  resolved "http://npm.htsc/fs-extra/download/fs-extra-10.1.0.tgz#02873cfbc4084dde127eaa5f9905eef2325d1abf"
  integrity sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^8.1.0:
  version "8.1.0"
  resolved "http://npm.htsc/fs-extra/download/fs-extra-8.1.0.tgz#49d43c45a88cd9677668cb7be1b46efdb8d2e1c0"
  integrity sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-monkey@^1.0.3:
  version "1.0.3"
  resolved "http://npm.htsc/fs-monkey/download/fs-monkey-1.0.3.tgz#ae3ac92d53bb328efe0e9a1d9541f6ad8d48e2d3"
  integrity sha1-rjrJLVO7Mo7+DpodlUH2rY1I4tM=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^2.3.2, fsevents@~2.3.2:
  version "2.3.2"
  resolved "http://npm.htsc/fsevents/download/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"
  integrity sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=

function-bind@^1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.htsc/function-bind/-/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

function.prototype.name@^1.1.5:
  version "1.1.5"
  resolved "http://npm.htsc/function.prototype.name/download/function.prototype.name-1.1.5.tgz#cce0505fe1ffb80503e6f9e46cc64e46a12a9621"
  integrity sha1-zOBQX+H/uAUD5vnkbMZORqEqliE=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"
    functions-have-names "^1.2.2"

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

functions-have-names@^1.2.2:
  version "1.2.3"
  resolved "http://npm.htsc/functions-have-names/download/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

generate-function@^2.3.1:
  version "2.3.1"
  resolved "http://npm.htsc/generate-function/download/generate-function-2.3.1.tgz#f069617690c10c868e73b8465746764f97c3479f"
  integrity sha1-8GlhdpDBDIaOc7hGV0Z2T5fDR58=
  dependencies:
    is-property "^1.0.2"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://npm.htsc/gensync/download/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "http://npm.htsc/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.0.2, get-intrinsic@^1.1.0, get-intrinsic@^1.1.1, get-intrinsic@^1.1.3:
  version "1.1.3"
  resolved "http://npm.htsc/get-intrinsic/download/get-intrinsic-1.1.3.tgz#063c84329ad93e83893c7f4f243ef63ffa351385"
  integrity sha1-BjyEMprZPoOJPH9PJD72P/o1E4U=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.3"

get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "http://registry.npm.htsc/get-intrinsic/-/get-intrinsic-1.2.4.tgz#e385f5a4b5227d449c3eabbad05494ef0abbeadd"
  integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "http://npm.htsc/get-package-type/download/get-package-type-0.1.0.tgz#8de2d803cff44df3bc6c456e6668b36c3926e11a"
  integrity sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=

get-ready@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/get-ready/download/get-ready-1.0.0.tgz#f91817f1e9adecfea13a562adfc8de883ab34782"
  integrity sha1-+RgX8emt7P6hOlYq38jeiDqzR4I=

get-stream@^5.0.0:
  version "5.2.0"
  resolved "http://npm.htsc/get-stream/download/get-stream-5.2.0.tgz#4966a1795ee5ace65e706c4b7beb71257d6e22d3"
  integrity sha1-SWaheV7lrOZecGxLe+txJX1uItM=
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "http://npm.htsc/get-stream/download/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/get-symbol-description/download/get-symbol-description-1.0.0.tgz#7fdb81c900101fbd564dd5f1a30af5aadc1e58d6"
  integrity sha1-f9uByQAQH71WTdXxowr1qtweWNY=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

getpass@^0.1.1:
  version "0.1.7"
  resolved "http://npm.htsc/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://npm.htsc/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "http://npm.htsc/glob-to-regexp/download/glob-to-regexp-0.4.1.tgz#c75297087c851b9a578bd217dd59a92f59fe546e"
  integrity sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=

glob@^7.0.0, glob@^7.1.3, glob@^7.1.4, glob@^7.2.0:
  version "7.2.3"
  resolved "http://npm.htsc/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/global-modules/download/global-modules-2.0.0.tgz#997605ad2345f27f51539bea26574421215c7780"
  integrity sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/global-prefix/download/global-prefix-3.0.0.tgz#fc85f73064df69f50421f47f883fe5b913ba9b97"
  integrity sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://npm.htsc/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.6.0, globals@^13.9.0:
  version "13.17.0"
  resolved "http://npm.htsc/globals/download/globals-13.17.0.tgz#902eb1e680a41da93945adbdcb5a9f361ba69bd4"
  integrity sha1-kC6x5oCkHak5Ra29y1qfNhumm9Q=
  dependencies:
    type-fest "^0.20.2"

globby@^11.1.0:
  version "11.1.0"
  resolved "http://npm.htsc/globby/download/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

globjoin@^0.1.4:
  version "0.1.4"
  resolved "http://npm.htsc/globjoin/download/globjoin-0.1.4.tgz#2f4494ac8919e3767c5cbb691e9f463324285d43"
  integrity sha1-L0SUrIkZ43Z8XLtpHp9GMyQoXUM=

gopd@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/gopd/download/gopd-1.0.1.tgz#29ff76de69dac7489b7c0918a5788e56477c332c"
  integrity sha1-Kf923mnax0ibfAkYpXiOVkd8Myw=
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.4, graceful-fs@^4.2.9:
  version "4.2.10"
  resolved "http://npm.htsc/graceful-fs/download/graceful-fs-4.2.10.tgz#147d3a006da4ca3ce14728c7aefc287c367d7a6c"
  integrity sha1-FH06AG2kyjzhRyjHrvwofDZ9emw=

graphql-query-complexity@^0.12.0:
  version "0.12.0"
  resolved "http://npm.htsc/graphql-query-complexity/download/graphql-query-complexity-0.12.0.tgz#5f636ccc54da82225f31e898e7f27192fe074b4c"
  integrity sha1-X2NszFTagiJfMeiY5/Jxkv4HS0w=
  dependencies:
    lodash.get "^4.4.2"

graphql-subscriptions@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/graphql-subscriptions/download/graphql-subscriptions-2.0.0.tgz#11ec181d475852d8aec879183e8e1eb94f2eb79a"
  integrity sha1-EewYHUdYUtiuyHkYPo4euU8ut5o=
  dependencies:
    iterall "^1.3.0"

graphql-tag@2.12.6:
  version "2.12.6"
  resolved "http://npm.htsc/graphql-tag/download/graphql-tag-2.12.6.tgz#d441a569c1d2537ef10ca3d1633b48725329b5f1"
  integrity sha1-1EGlacHSU37xDKPRYztIclMptfE=
  dependencies:
    tslib "^2.1.0"

graphql-ws@5.12.1:
  version "5.12.1"
  resolved "http://npm.htsc/graphql-ws/download/graphql-ws-5.12.1.tgz#c62d5ac54dbd409cc6520b0b39de374b3d59d0dd"
  integrity sha1-xi1axU29QJzGUgsLOd43Sz1Z0N0=

graphql@^16.6.0:
  version "16.6.0"
  resolved "http://npm.htsc/graphql/download/graphql-16.6.0.tgz#c2dcffa4649db149f6282af726c8c83f1c7c5fdb"
  integrity sha1-wtz/pGSdsUn2KCr3JsjIPxx8X9s=

har-schema@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "http://npm.htsc/har-validator/download/har-validator-5.1.5.tgz#1f0803b9f8cb20c0fa13822df1ecddb36bde1efd"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

hard-rejection@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/hard-rejection/download/hard-rejection-2.1.0.tgz#1c6eda5c1685c63942766d79bb40ae773cecd883"
  integrity sha1-HG7aXBaFxjlCdm15u0Cudzzs2IM=

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/has-bigints/download/has-bigints-1.0.2.tgz#0871bd3e3d51626f6ca0966668ba35d5602d6eaa"
  integrity sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/has-property-descriptors/download/has-property-descriptors-1.0.0.tgz#610708600606d36961ed04c196193b6a607fa861"
  integrity sha1-YQcIYAYG02lh7QTBlhk7amB/qGE=
  dependencies:
    get-intrinsic "^1.1.1"

has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.htsc/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.htsc/has-proto/-/has-proto-1.0.3.tgz#b31ddfe9b0e6e9914536a6ab286426d0214f77fd"
  integrity sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "http://npm.htsc/has-symbols/download/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/has-tostringtag/download/has-tostringtag-1.0.0.tgz#7e133818a7d394734f941e73c3d3f9291e658b25"
  integrity sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=
  dependencies:
    has-symbols "^1.0.2"

has@^1.0.3:
  version "1.0.3"
  resolved "http://npm.htsc/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hasown@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.htsc/hasown/-/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

hexoid@1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/hexoid/download/hexoid-1.0.0.tgz#ad10c6573fb907de23d9ec63a711267d9dc9bc18"
  integrity sha1-rRDGVz+5B94j2exjpxEmfZ3JvBg=

highlight.js@^10.7.1:
  version "10.7.3"
  resolved "http://npm.htsc/highlight.js/download/highlight.js-10.7.3.tgz#697272e3991356e40c3cac566a74eef681756531"
  integrity sha1-aXJy45kTVuQMPKxWanTu9oF1ZTE=

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "http://npm.htsc/hosted-git-info/download/hosted-git-info-2.8.9.tgz#dffc0bf9a21c02209090f2aa69429e1414daf3f9"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

hosted-git-info@^4.0.1:
  version "4.1.0"
  resolved "http://npm.htsc/hosted-git-info/download/hosted-git-info-4.1.0.tgz#827b82867e9ff1c8d0c4d9d53880397d2c86d224"
  integrity sha1-gnuChn6f8cjQxNnVOIA5fSyG0iQ=
  dependencies:
    lru-cache "^6.0.0"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "http://npm.htsc/html-escaper/download/html-escaper-2.0.2.tgz#dfd60027da36a36dfcbe236262c00a5822681453"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

html-tags@^3.2.0:
  version "3.2.0"
  resolved "http://npm.htsc/html-tags/download/html-tags-3.2.0.tgz#dbb3518d20b726524e4dd43de397eb0a95726961"
  integrity sha1-27NRjSC3JlJOTdQ945frCpVyaWE=

htmlparser2@^8.0.0:
  version "8.0.1"
  resolved "http://npm.htsc/htmlparser2/download/htmlparser2-8.0.1.tgz#abaa985474fcefe269bc761a779b544d7196d010"
  integrity sha1-q6qYVHT87+JpvHYad5tUTXGW0BA=
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    domutils "^3.0.1"
    entities "^4.3.0"

http-errors@2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/http-errors/download/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/http-signature/download/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

human-signals@^1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/human-signals/download/human-signals-1.1.1.tgz#c5b1cd14f50aeae09ab6c59fe63ba3395fe4dfa3"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

human-signals@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/human-signals/download/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

husky@7.0.4:
  version "7.0.4"
  resolved "http://npm.htsc/husky/download/husky-7.0.4.tgz#242048245dc49c8fb1bf0cc7cfb98dd722531535"
  integrity sha1-JCBIJF3EnI+xvwzHz7mN1yJTFTU=

iconv-lite@0.4.24, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "http://npm.htsc/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.5.0:
  version "0.5.2"
  resolved "http://npm.htsc/iconv-lite/download/iconv-lite-0.5.2.tgz#af6d628dccfb463b7364d97f715e4b74b8c8c2b8"
  integrity sha1-r21ijcz7RjtzZNl/cV5LdLjIwrg=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "http://npm.htsc/iconv-lite/download/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.1.13, ieee754@^1.2.1:
  version "1.2.1"
  resolved "http://npm.htsc/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore@^4.0.6:
  version "4.0.6"
  resolved "http://npm.htsc/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.0.5, ignore@^5.1.1, ignore@^5.2.0:
  version "5.2.0"
  resolved "http://npm.htsc/ignore/download/ignore-5.2.0.tgz#6d3bac8fa7fe0d45d9f9be7bac2fc279577e345a"
  integrity sha1-bTusj6f+DUXZ+b57rC/CeVd+NFo=

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "http://npm.htsc/import-fresh/download/import-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-lazy@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/import-lazy/download/import-lazy-4.0.0.tgz#e8eb627483a0a43da3c03f3e35548be5cb0cc153"
  integrity sha1-6OtidIOgpD2jwD8+NVSL5csMwVM=

import-local@^3.0.2:
  version "3.1.0"
  resolved "http://npm.htsc/import-local/download/import-local-3.1.0.tgz#b4479df8a5fd44f6cdce24070675676063c95cb4"
  integrity sha1-tEed+KX9RPbNziQHBnVnYGPJXLQ=
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://npm.htsc/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/indent-string/download/indent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://npm.htsc/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://npm.htsc/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

ini@^1.3.5:
  version "1.3.8"
  resolved "http://npm.htsc/ini/download/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

inquirer@7.3.3:
  version "7.3.3"
  resolved "http://npm.htsc/inquirer/download/inquirer-7.3.3.tgz#04d176b2af04afc157a83fd7c100e98ee0aad003"
  integrity sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.19"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

inquirer@8.2.4:
  version "8.2.4"
  resolved "http://npm.htsc/inquirer/download/inquirer-8.2.4.tgz#ddbfe86ca2f67649a67daa6f1051c128f684f0b4"
  integrity sha1-3b/obKL2dkmmfapvEFHBKPaE8LQ=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.1"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.21"
    mute-stream "0.0.8"
    ora "^5.4.1"
    run-async "^2.4.0"
    rxjs "^7.5.5"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"
    wrap-ansi "^7.0.0"

inquirer@9.2.15:
  version "9.2.15"
  resolved "http://registry.npm.htsc/inquirer/-/inquirer-9.2.15.tgz#2135a36190a6e5c92f5d205e0af1fea36b9d3492"
  integrity sha512-vI2w4zl/mDluHt9YEQ/543VTCwPKWiHzKtm9dM2V0NdFcqEexDAjUHzO1oA60HRNaVifGXXM1tRRNluLVHa0Kg==
  dependencies:
    "@ljharb/through" "^2.3.12"
    ansi-escapes "^4.3.2"
    chalk "^5.3.0"
    cli-cursor "^3.1.0"
    cli-width "^4.1.0"
    external-editor "^3.1.0"
    figures "^3.2.0"
    lodash "^4.17.21"
    mute-stream "1.0.0"
    ora "^5.4.1"
    run-async "^3.0.0"
    rxjs "^7.8.1"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    wrap-ansi "^6.2.0"

internal-slot@^1.0.3:
  version "1.0.3"
  resolved "http://npm.htsc/internal-slot/download/internal-slot-1.0.3.tgz#7347e307deeea2faac2ac6205d4bc7d34967f59c"
  integrity sha1-c0fjB97uovqsKsYgXUvH00ln9Zw=
  dependencies:
    get-intrinsic "^1.1.0"
    has "^1.0.3"
    side-channel "^1.0.4"

interpret@^1.0.0, interpret@^1.4.0:
  version "1.4.0"
  resolved "http://npm.htsc/interpret/download/interpret-1.4.0.tgz#665ab8bc4da27a774a40584e812e3e0fa45b1a1e"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

ioredis@5.3.0:
  version "5.3.0"
  resolved "http://npm.htsc/ioredis/download/ioredis-5.3.0.tgz#b5469f0fd374648ef074840c00c1d8eed42fca3f"
  integrity sha1-tUafD9N0ZI7wdIQMAMHY7tQvyj8=
  dependencies:
    "@ioredis/commands" "^1.1.1"
    cluster-key-slot "^1.1.0"
    debug "^4.3.4"
    denque "^2.1.0"
    lodash.defaults "^4.2.0"
    lodash.isarguments "^3.1.0"
    redis-errors "^1.2.0"
    redis-parser "^3.0.0"
    standard-as-callback "^2.1.0"

ip@^1.1.5:
  version "1.1.8"
  resolved "http://npm.htsc/ip/download/ip-1.1.8.tgz#ae05948f6b075435ed3307acce04629da8cdbf48"
  integrity sha1-rgWUj2sHVDXtMweszgRinajNv0g=

ip@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.htsc/ip/-/ip-2.0.1.tgz#e8f3595d33a3ea66490204234b77636965307105"
  integrity sha512-lJUL9imLTNi1ZfXT+DU6rBBdbiKGBuay9B6xGSPVjUeQwaH1RIGqef8RZkUtHioLmSNpPR5M4HVKJGm1j8FWVQ==

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "http://npm.htsc/ipaddr.js/download/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-alphabetical@^1.0.0:
  version "1.0.4"
  resolved "http://npm.htsc/is-alphabetical/download/is-alphabetical-1.0.4.tgz#9e7d6b94916be22153745d184c298cbf986a686d"
  integrity sha1-nn1rlJFr4iFTdF0YTCmMv5hqaG0=

is-alphanumerical@^1.0.0:
  version "1.0.4"
  resolved "http://npm.htsc/is-alphanumerical/download/is-alphanumerical-1.0.4.tgz#7eb9a2431f855f6b1ef1a78e326df515696c4dbf"
  integrity sha1-frmiQx+FX2se8aeOMm31FWlsTb8=
  dependencies:
    is-alphabetical "^1.0.0"
    is-decimal "^1.0.0"

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "http://npm.htsc/is-arguments/download/is-arguments-1.1.1.tgz#15b3f88fda01f2a97fec84ca761a560f123efa9b"
  integrity sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://npm.htsc/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "http://npm.htsc/is-arrayish/download/is-arrayish-0.3.2.tgz#4574a2ae56f7ab206896fb431eaeed066fdf8f03"
  integrity sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "http://npm.htsc/is-bigint/download/is-bigint-1.0.4.tgz#08147a1875bc2b32005d41ccd8291dffc6691df3"
  integrity sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "http://npm.htsc/is-boolean-object/download/is-boolean-object-1.1.2.tgz#5c6dc200246dd9321ae4b885a114bb1f75f63719"
  integrity sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"
  resolved "http://npm.htsc/is-callable/download/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-core-module@^2.5.0, is-core-module@^2.7.0, is-core-module@^2.8.1, is-core-module@^2.9.0:
  version "2.11.0"
  resolved "http://npm.htsc/is-core-module/download/is-core-module-2.11.0.tgz#ad4cb3e3863e814523c96f3f58d26cc570ff0144"
  integrity sha1-rUyz44Y+gUUjyW8/WNJsxXD/AUQ=
  dependencies:
    has "^1.0.3"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "http://npm.htsc/is-date-object/download/is-date-object-1.0.5.tgz#0841d5536e724c25597bf6ea62e1bd38298df31f"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-decimal@^1.0.0:
  version "1.0.4"
  resolved "http://npm.htsc/is-decimal/download/is-decimal-1.0.4.tgz#65a3a5958a1c5b63a706e1b333d7cd9f630d3fa5"
  integrity sha1-ZaOllYocW2OnBuGzM9fNn2MNP6U=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://npm.htsc/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "http://npm.htsc/is-generator-fn/download/is-generator-fn-2.1.0.tgz#7d140adc389aaf3011a8f2a2a4cfa6faadffb118"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-generator-function@^1.0.7:
  version "1.0.10"
  resolved "http://npm.htsc/is-generator-function/download/is-generator-function-1.0.10.tgz#f1558baf1ac17e0deea7c0415c438351ff2b3c72"
  integrity sha1-8VWLrxrBfg3up8BBXEODUf8rPHI=
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://npm.htsc/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-hexadecimal@^1.0.0:
  version "1.0.4"
  resolved "http://npm.htsc/is-hexadecimal/download/is-hexadecimal-1.0.4.tgz#cc35c97588da4bd49a8eedd6bc4082d44dcb23a7"
  integrity sha1-zDXJdYjaS9Saju3WvECC1E3LI6c=

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/is-interactive/download/is-interactive-1.0.0.tgz#cea6e6ae5c870a7b0a0004070b7b587e0252912e"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-negative-zero@^2.0.2:
  version "2.0.2"
  resolved "http://npm.htsc/is-negative-zero/download/is-negative-zero-2.0.2.tgz#7bf6f03a28003b8b3965de3ac26f664d765f3150"
  integrity sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA=

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "http://npm.htsc/is-number-object/download/is-number-object-1.0.7.tgz#59d50ada4c45251784e9904f5246c742f07a42fc"
  integrity sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw=
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://npm.htsc/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-plain-obj@^1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/is-plain-obj/download/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-object@^5.0.0:
  version "5.0.0"
  resolved "http://npm.htsc/is-plain-object/download/is-plain-object-5.0.0.tgz#4427f50ab3429e9025ea7d52e9043a9ef4159344"
  integrity sha1-RCf1CrNCnpAl6n1S6QQ6nvQVk0Q=

is-property@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/is-property/download/is-property-1.0.2.tgz#57fe1c4e48474edd65b09911f26b1cd4095dda84"
  integrity sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ=

is-regex@^1.1.4:
  version "1.1.4"
  resolved "http://npm.htsc/is-regex/download/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-shared-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/is-shared-array-buffer/download/is-shared-array-buffer-1.0.2.tgz#8f259c573b60b6a32d4058a1a07430c0a7344c79"
  integrity sha1-jyWcVztgtqMtQFihoHQwwKc0THk=
  dependencies:
    call-bind "^1.0.2"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "http://npm.htsc/is-stream/download/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "http://npm.htsc/is-string/download/is-string-1.0.7.tgz#0dd12bf2006f255bb58f695110eff7491eebc0fd"
  integrity sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "http://npm.htsc/is-symbol/download/is-symbol-1.0.4.tgz#a6dac93b635b063ca6872236de88910a57af139c"
  integrity sha1-ptrJO2NbBjymhyI23oiRClevE5w=
  dependencies:
    has-symbols "^1.0.2"

is-typed-array@^1.1.10, is-typed-array@^1.1.3:
  version "1.1.10"
  resolved "http://npm.htsc/is-typed-array/download/is-typed-array-1.1.10.tgz#36a5b5cb4189b575d1a3e4b08536bfb485801e3f"
  integrity sha1-NqW1y0GJtXXRo+SwhTa/tIWAHj8=
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "http://npm.htsc/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz#3f26c76a809593b52bfa2ecb5710ed2779b522a7"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/is-weakref/download/is-weakref-1.0.2.tgz#9529f383a9338205e89765e0392efc2f100f06f2"
  integrity sha1-lSnzg6kzggXol2XgOS78LxAPBvI=
  dependencies:
    call-bind "^1.0.2"

isarray@~1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isstream@~0.1.2:
  version "0.1.2"
  resolved "http://npm.htsc/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.0"
  resolved "http://npm.htsc/istanbul-lib-coverage/download/istanbul-lib-coverage-3.2.0.tgz#189e7909d0a39fa5a3dfad5b03f71947770191d3"
  integrity sha1-GJ55CdCjn6Wj361bA/cZR3cBkdM=

istanbul-lib-instrument@^5.0.4, istanbul-lib-instrument@^5.1.0:
  version "5.2.1"
  resolved "http://npm.htsc/istanbul-lib-instrument/download/istanbul-lib-instrument-5.2.1.tgz#d10c8885c2125574e1c231cacadf955675e1ce3d"
  integrity sha1-0QyIhcISVXThwjHKyt+VVnXhzj0=
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-report@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/istanbul-lib-report/download/istanbul-lib-report-3.0.0.tgz#7518fe52ea44de372f460a76b5ecda9ffb73d8a6"
  integrity sha1-dRj+UupE3jcvRgp2tezan/tz2KY=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^3.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  resolved "http://npm.htsc/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.1.tgz#895f3a709fcfba34c6de5a42939022f3e4358551"
  integrity sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.1.3:
  version "3.1.5"
  resolved "http://npm.htsc/istanbul-reports/download/istanbul-reports-3.1.5.tgz#cc9a6ab25cb25659810e4785ed9d9fb742578bae"
  integrity sha1-zJpqslyyVlmBDkeF7Z2ft0JXi64=
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

iterall@1.3.0, iterall@^1.2.1, iterall@^1.3.0:
  version "1.3.0"
  resolved "http://npm.htsc/iterall/download/iterall-1.3.0.tgz#afcb08492e2915cbd8a0884eb93a8c94d0d72fea"
  integrity sha1-r8sISS4pFcvYoIhOuTqMlNDXL+o=

iterare@1.2.1:
  version "1.2.1"
  resolved "http://npm.htsc/iterare/download/iterare-1.2.1.tgz#139c400ff7363690e33abffa33cbba8920f00042"
  integrity sha1-E5xAD/c2NpDjOr/6M8u6iSDwAEI=

jake@^10.8.5:
  version "10.8.5"
  resolved "http://npm.htsc/jake/download/jake-10.8.5.tgz#f2183d2c59382cb274226034543b9c03b8164c46"
  integrity sha1-8hg9LFk4LLJ0ImA0VDucA7gWTEY=
  dependencies:
    async "^3.2.3"
    chalk "^4.0.2"
    filelist "^1.0.1"
    minimatch "^3.0.4"

jest-changed-files@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-changed-files/download/jest-changed-files-28.1.3.tgz#d9aeee6792be3686c47cb988a8eaf82ff4238831"
  integrity sha1-2a7uZ5K+NobEfLmIqOr4L/QjiDE=
  dependencies:
    execa "^5.0.0"
    p-limit "^3.1.0"

jest-circus@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-circus/download/jest-circus-28.1.3.tgz#d14bd11cf8ee1a03d69902dc47b6bd4634ee00e4"
  integrity sha1-0UvRHPjuGgPWmQLcR7a9RjTuAOQ=
  dependencies:
    "@jest/environment" "^28.1.3"
    "@jest/expect" "^28.1.3"
    "@jest/test-result" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    dedent "^0.7.0"
    is-generator-fn "^2.0.0"
    jest-each "^28.1.3"
    jest-matcher-utils "^28.1.3"
    jest-message-util "^28.1.3"
    jest-runtime "^28.1.3"
    jest-snapshot "^28.1.3"
    jest-util "^28.1.3"
    p-limit "^3.1.0"
    pretty-format "^28.1.3"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-cli@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-cli/download/jest-cli-28.1.3.tgz#558b33c577d06de55087b8448d373b9f654e46b2"
  integrity sha1-VYszxXfQbeVQh7hEjTc7n2VORrI=
  dependencies:
    "@jest/core" "^28.1.3"
    "@jest/test-result" "^28.1.3"
    "@jest/types" "^28.1.3"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    import-local "^3.0.2"
    jest-config "^28.1.3"
    jest-util "^28.1.3"
    jest-validate "^28.1.3"
    prompts "^2.0.1"
    yargs "^17.3.1"

jest-config@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-config/download/jest-config-28.1.3.tgz#e315e1f73df3cac31447eed8b8740a477392ec60"
  integrity sha1-4xXh9z3zysMUR+7YuHQKR3OS7GA=
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/test-sequencer" "^28.1.3"
    "@jest/types" "^28.1.3"
    babel-jest "^28.1.3"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    deepmerge "^4.2.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-circus "^28.1.3"
    jest-environment-node "^28.1.3"
    jest-get-type "^28.0.2"
    jest-regex-util "^28.0.2"
    jest-resolve "^28.1.3"
    jest-runner "^28.1.3"
    jest-util "^28.1.3"
    jest-validate "^28.1.3"
    micromatch "^4.0.4"
    parse-json "^5.2.0"
    pretty-format "^28.1.3"
    slash "^3.0.0"
    strip-json-comments "^3.1.1"

jest-diff@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-diff/download/jest-diff-28.1.3.tgz#948a192d86f4e7a64c5264ad4da4877133d8792f"
  integrity sha1-lIoZLYb056ZMUmStTaSHcTPYeS8=
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^28.1.1"
    jest-get-type "^28.0.2"
    pretty-format "^28.1.3"

jest-docblock@^28.1.1:
  version "28.1.1"
  resolved "http://npm.htsc/jest-docblock/download/jest-docblock-28.1.1.tgz#6f515c3bf841516d82ecd57a62eed9204c2f42a8"
  integrity sha1-b1FcO/hBUW2C7NV6Yu7ZIEwvQqg=
  dependencies:
    detect-newline "^3.0.0"

jest-each@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-each/download/jest-each-28.1.3.tgz#bdd1516edbe2b1f3569cfdad9acd543040028f81"
  integrity sha1-vdFRbtvisfNWnP2tms1UMEACj4E=
  dependencies:
    "@jest/types" "^28.1.3"
    chalk "^4.0.0"
    jest-get-type "^28.0.2"
    jest-util "^28.1.3"
    pretty-format "^28.1.3"

jest-environment-node@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-environment-node/download/jest-environment-node-28.1.3.tgz#7e74fe40eb645b9d56c0c4b70ca4357faa349be5"
  integrity sha1-fnT+QOtkW51WwMS3DKQ1f6o0m+U=
  dependencies:
    "@jest/environment" "^28.1.3"
    "@jest/fake-timers" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    jest-mock "^28.1.3"
    jest-util "^28.1.3"

jest-get-type@^28.0.2:
  version "28.0.2"
  resolved "http://npm.htsc/jest-get-type/download/jest-get-type-28.0.2.tgz#34622e628e4fdcd793d46db8a242227901fcf203"
  integrity sha1-NGIuYo5P3NeT1G24okIieQH88gM=

jest-haste-map@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-haste-map/download/jest-haste-map-28.1.3.tgz#abd5451129a38d9841049644f34b034308944e2b"
  integrity sha1-q9VFESmjjZhBBJZE80sDQwiUTis=
  dependencies:
    "@jest/types" "^28.1.3"
    "@types/graceful-fs" "^4.1.3"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.9"
    jest-regex-util "^28.0.2"
    jest-util "^28.1.3"
    jest-worker "^28.1.3"
    micromatch "^4.0.4"
    walker "^1.0.8"
  optionalDependencies:
    fsevents "^2.3.2"

jest-leak-detector@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-leak-detector/download/jest-leak-detector-28.1.3.tgz#a6685d9b074be99e3adee816ce84fd30795e654d"
  integrity sha1-pmhdmwdL6Z463ugWzoT9MHleZU0=
  dependencies:
    jest-get-type "^28.0.2"
    pretty-format "^28.1.3"

jest-matcher-utils@^28.0.0, jest-matcher-utils@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-matcher-utils/download/jest-matcher-utils-28.1.3.tgz#5a77f1c129dd5ba3b4d7fc20728806c78893146e"
  integrity sha1-WnfxwSndW6O01/wgcogGx4iTFG4=
  dependencies:
    chalk "^4.0.0"
    jest-diff "^28.1.3"
    jest-get-type "^28.0.2"
    pretty-format "^28.1.3"

jest-message-util@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-message-util/download/jest-message-util-28.1.3.tgz#232def7f2e333f1eecc90649b5b94b0055e7c43d"
  integrity sha1-Iy3vfy4zPx7syQZJtblLAFXnxD0=
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^28.1.3"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    micromatch "^4.0.4"
    pretty-format "^28.1.3"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-mock@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-mock/download/jest-mock-28.1.3.tgz#d4e9b1fc838bea595c77ab73672ebf513ab249da"
  integrity sha1-1Omx/IOL6llcd6tzZy6/UTqySdo=
  dependencies:
    "@jest/types" "^28.1.3"
    "@types/node" "*"

jest-pnp-resolver@^1.2.2:
  version "1.2.2"
  resolved "http://npm.htsc/jest-pnp-resolver/download/jest-pnp-resolver-1.2.2.tgz#b704ac0ae028a89108a4d040b3f919dfddc8e33c"
  integrity sha1-twSsCuAoqJEIpNBAs/kZ393I4zw=

jest-regex-util@^28.0.2:
  version "28.0.2"
  resolved "http://npm.htsc/jest-regex-util/download/jest-regex-util-28.0.2.tgz#afdc377a3b25fb6e80825adcf76c854e5bf47ead"
  integrity sha1-r9w3ejsl+26Aglrc92yFTlv0fq0=

jest-resolve-dependencies@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-resolve-dependencies/download/jest-resolve-dependencies-28.1.3.tgz#8c65d7583460df7275c6ea2791901fa975c1fe66"
  integrity sha1-jGXXWDRg33J1xuonkZAfqXXB/mY=
  dependencies:
    jest-regex-util "^28.0.2"
    jest-snapshot "^28.1.3"

jest-resolve@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-resolve/download/jest-resolve-28.1.3.tgz#cfb36100341ddbb061ec781426b3c31eb51aa0a8"
  integrity sha1-z7NhADQd27Bh7HgUJrPDHrUaoKg=
  dependencies:
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^28.1.3"
    jest-pnp-resolver "^1.2.2"
    jest-util "^28.1.3"
    jest-validate "^28.1.3"
    resolve "^1.20.0"
    resolve.exports "^1.1.0"
    slash "^3.0.0"

jest-runner@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-runner/download/jest-runner-28.1.3.tgz#5eee25febd730b4713a2cdfd76bdd5557840f9a1"
  integrity sha1-Xu4l/r1zC0cTos39dr3VVXhA+aE=
  dependencies:
    "@jest/console" "^28.1.3"
    "@jest/environment" "^28.1.3"
    "@jest/test-result" "^28.1.3"
    "@jest/transform" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.10.2"
    graceful-fs "^4.2.9"
    jest-docblock "^28.1.1"
    jest-environment-node "^28.1.3"
    jest-haste-map "^28.1.3"
    jest-leak-detector "^28.1.3"
    jest-message-util "^28.1.3"
    jest-resolve "^28.1.3"
    jest-runtime "^28.1.3"
    jest-util "^28.1.3"
    jest-watcher "^28.1.3"
    jest-worker "^28.1.3"
    p-limit "^3.1.0"
    source-map-support "0.5.13"

jest-runtime@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-runtime/download/jest-runtime-28.1.3.tgz#a57643458235aa53e8ec7821949e728960d0605f"
  integrity sha1-pXZDRYI1qlPo7HghlJ5yiWDQYF8=
  dependencies:
    "@jest/environment" "^28.1.3"
    "@jest/fake-timers" "^28.1.3"
    "@jest/globals" "^28.1.3"
    "@jest/source-map" "^28.1.2"
    "@jest/test-result" "^28.1.3"
    "@jest/transform" "^28.1.3"
    "@jest/types" "^28.1.3"
    chalk "^4.0.0"
    cjs-module-lexer "^1.0.0"
    collect-v8-coverage "^1.0.0"
    execa "^5.0.0"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-haste-map "^28.1.3"
    jest-message-util "^28.1.3"
    jest-mock "^28.1.3"
    jest-regex-util "^28.0.2"
    jest-resolve "^28.1.3"
    jest-snapshot "^28.1.3"
    jest-util "^28.1.3"
    slash "^3.0.0"
    strip-bom "^4.0.0"

jest-snapshot@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-snapshot/download/jest-snapshot-28.1.3.tgz#17467b3ab8ddb81e2f605db05583d69388fc0668"
  integrity sha1-F0Z7OrjduB4vYF2wVYPWk4j8Bmg=
  dependencies:
    "@babel/core" "^7.11.6"
    "@babel/generator" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/traverse" "^7.7.2"
    "@babel/types" "^7.3.3"
    "@jest/expect-utils" "^28.1.3"
    "@jest/transform" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/babel__traverse" "^7.0.6"
    "@types/prettier" "^2.1.5"
    babel-preset-current-node-syntax "^1.0.0"
    chalk "^4.0.0"
    expect "^28.1.3"
    graceful-fs "^4.2.9"
    jest-diff "^28.1.3"
    jest-get-type "^28.0.2"
    jest-haste-map "^28.1.3"
    jest-matcher-utils "^28.1.3"
    jest-message-util "^28.1.3"
    jest-util "^28.1.3"
    natural-compare "^1.4.0"
    pretty-format "^28.1.3"
    semver "^7.3.5"

jest-util@^28.0.0, jest-util@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-util/download/jest-util-28.1.3.tgz#f4f932aa0074f0679943220ff9cbba7e497028b0"
  integrity sha1-9PkyqgB08GeZQyIP+cu6fklwKLA=
  dependencies:
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-validate@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-validate/download/jest-validate-28.1.3.tgz#e322267fd5e7c64cea4629612c357bbda96229df"
  integrity sha1-4yImf9XnxkzqRilhLDV7valiKd8=
  dependencies:
    "@jest/types" "^28.1.3"
    camelcase "^6.2.0"
    chalk "^4.0.0"
    jest-get-type "^28.0.2"
    leven "^3.1.0"
    pretty-format "^28.1.3"

jest-watcher@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-watcher/download/jest-watcher-28.1.3.tgz#c6023a59ba2255e3b4c57179fc94164b3e73abd4"
  integrity sha1-xgI6WboiVeO0xXF5/JQWSz5zq9Q=
  dependencies:
    "@jest/test-result" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    emittery "^0.10.2"
    jest-util "^28.1.3"
    string-length "^4.0.1"

jest-worker@^27.4.5:
  version "27.5.1"
  resolved "http://npm.htsc/jest-worker/download/jest-worker-27.5.1.tgz#8d146f0900e8973b106b6f73cc1e9a8cb86f8db0"
  integrity sha1-jRRvCQDolzsQa29zzB6ajLhvjbA=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest-worker/download/jest-worker-28.1.3.tgz#7e3c4ce3fa23d1bb6accb169e7f396f98ed4bb98"
  integrity sha1-fjxM4/oj0btqzLFp5/OW+Y7Uu5g=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/jest/download/jest-28.1.3.tgz#e9c6a7eecdebe3548ca2b18894a50f45b36dfc6b"
  integrity sha1-6can7s3r41SMorGIlKUPRbNt/Gs=
  dependencies:
    "@jest/core" "^28.1.3"
    "@jest/types" "^28.1.3"
    import-local "^3.0.2"
    jest-cli "^28.1.3"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-tokens@^8.0.0:
  version "8.0.0"
  resolved "http://npm.htsc/js-tokens/download/js-tokens-8.0.0.tgz#5dbe2cdfa9afc93251d3a77bf18c3ad6fa8a4de4"
  integrity sha1-Xb4s36mvyTJR06d78Yw61vqKTeQ=

js-yaml@4.1.0, js-yaml@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/js-yaml/download/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "http://npm.htsc/js-yaml/download/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "http://npm.htsc/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsesc@^2.5.1:
  version "2.5.2"
  resolved "http://npm.htsc/jsesc/download/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "http://npm.htsc/jsesc/download/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "http://npm.htsc/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://npm.htsc/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-schema@0.4.0:
  version "0.4.0"
  resolved "http://npm.htsc/json-schema/download/json-schema-0.4.0.tgz#f7de4cf6efab838ebaeb3236474cbba5a1930ab5"
  integrity sha1-995M9u+rg4666zI2R0y7paGTCrU=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "http://npm.htsc/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json5@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/json5/download/json5-1.0.1.tgz#779fb0018604fa854eacbf6252180d83543e3dbe"
  integrity sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=
  dependencies:
    minimist "^1.2.0"

json5@^2.2.1:
  version "2.2.1"
  resolved "http://npm.htsc/json5/download/json5-2.2.1.tgz#655d50ed1e6f95ad1a3caababd2b0efda10b395c"
  integrity sha1-ZV1Q7R5vla0aPKq6vSsO/aELOVw=

jsonc-parser@3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/jsonc-parser/download/jsonc-parser-3.0.0.tgz#abdd785701c7e7eaca8a9ec8cf070ca51a745a22"
  integrity sha1-q914VwHH5+rKip7IzwcMpRp0WiI=

jsonc-parser@3.2.1:
  version "3.2.1"
  resolved "http://registry.npm.htsc/jsonc-parser/-/jsonc-parser-3.2.1.tgz#031904571ccf929d7670ee8c547545081cb37f1a"
  integrity sha512-AilxAyFOAcK5wA1+LeaySVBrHsGQvUFCDWXKpZjzaL0PqW+xfBOttn8GNtWKFWqneyMZj41MWF9Kl6iPWLwgOA==

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/jsonfile/download/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "http://npm.htsc/jsonfile/download/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonwebtoken@8.5.1, jsonwebtoken@^8.2.0:
  version "8.5.1"
  resolved "http://npm.htsc/jsonwebtoken/download/jsonwebtoken-8.5.1.tgz#00e71e0b8df54c2121a1f26137df2280673bcc0d"
  integrity sha1-AOceC431TCEhofJhN98igGc7zA0=
  dependencies:
    jws "^3.2.2"
    lodash.includes "^4.3.0"
    lodash.isboolean "^3.0.3"
    lodash.isinteger "^4.0.4"
    lodash.isnumber "^3.0.3"
    lodash.isplainobject "^4.0.6"
    lodash.isstring "^4.0.1"
    lodash.once "^4.0.0"
    ms "^2.1.1"
    semver "^5.6.0"

jsprim@^1.2.2:
  version "1.4.2"
  resolved "http://npm.htsc/jsprim/download/jsprim-1.4.2.tgz#712c65533a15c878ba59e9ed5f0e26d5b77c5feb"
  integrity sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.4.0"
    verror "1.10.0"

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.2:
  version "3.3.3"
  resolved "http://npm.htsc/jsx-ast-utils/download/jsx-ast-utils-3.3.3.tgz#76b3e6e6cece5c69d49a5792c3d01bd1a0cdc7ea"
  integrity sha1-drPm5s7OXGnUmleSw9Ab0aDNx+o=
  dependencies:
    array-includes "^3.1.5"
    object.assign "^4.1.3"

jwa@^1.4.1:
  version "1.4.1"
  resolved "http://npm.htsc/jwa/download/jwa-1.4.1.tgz#743c32985cb9e98655530d53641b66c8645b039a"
  integrity sha1-dDwymFy56YZVUw1TZBtmyGRbA5o=
  dependencies:
    buffer-equal-constant-time "1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jws@^3.2.2:
  version "3.2.2"
  resolved "http://npm.htsc/jws/download/jws-3.2.2.tgz#001099f3639468c9414000e99995fa52fb478304"
  integrity sha1-ABCZ82OUaMlBQADpmZX6UvtHgwQ=
  dependencies:
    jwa "^1.4.1"
    safe-buffer "^5.0.1"

kafkajs@^1.15.0:
  version "1.16.0"
  resolved "http://npm.htsc/kafkajs/download/kafkajs-1.16.0.tgz#bfcc3ae2b69265ca8435b53a01ee9e8787b9fee5"
  integrity sha1-v8w64raSZcqENbU6Ae6eh4e5/uU=

kind-of@^6.0.2, kind-of@^6.0.3:
  version "6.0.3"
  resolved "http://npm.htsc/kind-of/download/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

kleur@^3.0.3:
  version "3.0.3"
  resolved "http://npm.htsc/kleur/download/kleur-3.0.3.tgz#a79c9ecc86ee1ce3fa6206d1216c501f147fc07e"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

known-css-properties@^0.25.0:
  version "0.25.0"
  resolved "http://npm.htsc/known-css-properties/download/known-css-properties-0.25.0.tgz#6ebc4d4b412f602e5cfbeb4086bd544e34c0a776"
  integrity sha1-brxNS0EvYC5c++tAhr1UTjTAp3Y=

known-css-properties@^0.26.0:
  version "0.26.0"
  resolved "http://npm.htsc/known-css-properties/download/known-css-properties-0.26.0.tgz#008295115abddc045a9f4ed7e2a84dc8b3a77649"
  integrity sha1-AIKVEVq93ARan07X4qhNyLOndkk=

kuler@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/kuler/download/kuler-2.0.0.tgz#e2c570a3800388fb44407e851531c1d670b061b3"
  integrity sha1-4sVwo4ADiPtEQH6FFTHB1nCwYbM=

language-subtag-registry@~0.3.2:
  version "0.3.22"
  resolved "http://npm.htsc/language-subtag-registry/download/language-subtag-registry-0.3.22.tgz#2e1500861b2e457eba7e7ae86877cbd08fa1fd1d"
  integrity sha1-LhUAhhsuRX66fnroaHfL0I+h/R0=

language-tags@^1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/language-tags/download/language-tags-1.0.5.tgz#d321dbc4da30ba8bf3024e040fa5c14661f9193a"
  integrity sha1-0yHbxNowuovzAk4ED6XBRmH5GTo=
  dependencies:
    language-subtag-registry "~0.3.2"

leven@^3.1.0:
  version "3.1.0"
  resolved "http://npm.htsc/leven/download/leven-3.1.0.tgz#77891de834064cccba82ae7842bb6b14a13ed7f2"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levn@^0.4.1:
  version "0.4.1"
  resolved "http://npm.htsc/levn/download/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

libphonenumber-js@^1.9.43:
  version "1.10.14"
  resolved "http://npm.htsc/libphonenumber-js/download/libphonenumber-js-1.10.14.tgz#e29da7f539751f724ac54017a098e3c7ca23de94"
  integrity sha1-4p2n9Tl1H3JKxUAXoJjjx8oj3pQ=

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://npm.htsc/lines-and-columns/download/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

loader-runner@^4.2.0:
  version "4.3.0"
  resolved "http://npm.htsc/loader-runner/download/loader-runner-4.3.0.tgz#c1b4a163b99f614830353b16755e7149ac2314e1"
  integrity sha1-wbShY7mfYUgwNTsWdV5xSawjFOE=

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://npm.htsc/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash.camelcase@4.3.0:
  version "4.3.0"
  resolved "http://npm.htsc/lodash.camelcase/download/lodash.camelcase-4.3.0.tgz#b28aa6288a2b9fc651035c7711f65ab6190331a6"
  integrity sha1-soqmKIorn8ZRA1x3EfZathkDMaY=

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "http://npm.htsc/lodash.debounce/download/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.defaults@^4.2.0:
  version "4.2.0"
  resolved "http://npm.htsc/lodash.defaults/download/lodash.defaults-4.2.0.tgz#d09178716ffea4dde9e5fb7b37f6f0802274580c"
  integrity sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw=

lodash.get@^4.4.2:
  version "4.4.2"
  resolved "http://npm.htsc/lodash.get/download/lodash.get-4.4.2.tgz#2d177f652fa31e939b4438d5341499dfa3825e99"
  integrity sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk=

lodash.includes@^4.3.0:
  version "4.3.0"
  resolved "http://npm.htsc/lodash.includes/download/lodash.includes-4.3.0.tgz#60bb98a87cb923c68ca1e51325483314849f553f"
  integrity sha1-YLuYqHy5I8aMoeUTJUgzFISfVT8=

lodash.isarguments@^3.1.0:
  version "3.1.0"
  resolved "http://npm.htsc/lodash.isarguments/download/lodash.isarguments-3.1.0.tgz#2f573d85c6a24289ff00663b491c1d338ff3458a"
  integrity sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo=

lodash.isboolean@^3.0.3:
  version "3.0.3"
  resolved "http://npm.htsc/lodash.isboolean/download/lodash.isboolean-3.0.3.tgz#6c2e171db2a257cd96802fd43b01b20d5f5870f6"
  integrity sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY=

lodash.isinteger@^4.0.4:
  version "4.0.4"
  resolved "http://npm.htsc/lodash.isinteger/download/lodash.isinteger-4.0.4.tgz#619c0af3d03f8b04c31f5882840b77b11cd68343"
  integrity sha1-YZwK89A/iwTDH1iChAt3sRzWg0M=

lodash.isnumber@^3.0.3:
  version "3.0.3"
  resolved "http://npm.htsc/lodash.isnumber/download/lodash.isnumber-3.0.3.tgz#3ce76810c5928d03352301ac287317f11c0b1ffc"
  integrity sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w=

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "http://npm.htsc/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz#7c526a52d89b45c45cc690b88163be0497f550cb"
  integrity sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=

lodash.isstring@^4.0.1:
  version "4.0.1"
  resolved "http://npm.htsc/lodash.isstring/download/lodash.isstring-4.0.1.tgz#d527dfb5456eca7cc9bb95d5daeaf88ba54a5451"
  integrity sha1-1SfftUVuynzJu5XV2ur4i6VKVFE=

lodash.kebabcase@4.1.1:
  version "4.1.1"
  resolved "http://npm.htsc/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz#8489b1cb0d29ff88195cceca448ff6d6cc295c36"
  integrity sha1-hImxyw0p/4gZXM7KRI/21swpXDY=

lodash.memoize@4.x:
  version "4.1.2"
  resolved "http://npm.htsc/lodash.memoize/download/lodash.memoize-4.1.2.tgz#bcc6c49a42a2840ed997f323eada5ecd182e0bfe"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://npm.htsc/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.omit@4.5.0:
  version "4.5.0"
  resolved "http://npm.htsc/lodash.omit/download/lodash.omit-4.5.0.tgz#6eb19ae5a1ee1dd9df0b969e66ce0b7fa30b5e60"
  integrity sha1-brGa5aHuHdnfC5aeZs4Lf6MLXmA=

lodash.once@^4.0.0:
  version "4.1.1"
  resolved "http://npm.htsc/lodash.once/download/lodash.once-4.1.1.tgz#0dd3971213c7c56df880977d504c88fb471a97ac"
  integrity sha1-DdOXEhPHxW34gJd9UEyI+0cal6w=

lodash.snakecase@4.1.1:
  version "4.1.1"
  resolved "http://npm.htsc/lodash.snakecase/download/lodash.snakecase-4.1.1.tgz#39d714a35357147837aefd64b5dcbb16becd8f8d"
  integrity sha1-OdcUo1NXFHg3rv1ktdy7Fr7Nj40=

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "http://npm.htsc/lodash.sortby/download/lodash.sortby-4.7.0.tgz#edd14c824e2cc9c1e0b0a1b42bb5210516a42438"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "http://npm.htsc/lodash.truncate/download/lodash.truncate-4.4.2.tgz#5a350da0b1113b837ecfffd5812cbe58d6eae193"
  integrity sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=

lodash.upperfirst@4.3.1:
  version "4.3.1"
  resolved "http://npm.htsc/lodash.upperfirst/download/lodash.upperfirst-4.3.1.tgz#1365edf431480481ef0d1c68957a5ed99d49f7ce"
  integrity sha1-E2Xt9DFIBIHvDRxolXpe2Z1J984=

lodash@4.17.21, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.21:
  version "4.17.21"
  resolved "http://registry.npm.htsc/lodash/-/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/log-symbols/download/log-symbols-4.1.0.tgz#3fbdbb95b4683ac9fc785111e792e558d4abd503"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

logform@^2.3.2, logform@^2.4.0:
  version "2.4.2"
  resolved "http://npm.htsc/logform/download/logform-2.4.2.tgz#a617983ac0334d0c3b942c34945380062795b47c"
  integrity sha1-pheYOsAzTQw7lCw0lFOABieVtHw=
  dependencies:
    "@colors/colors" "1.5.0"
    fecha "^4.2.0"
    ms "^2.1.1"
    safe-stable-stringify "^2.3.1"
    triple-beam "^1.3.0"

loglevel@^1.6.8:
  version "1.8.1"
  resolved "http://npm.htsc/loglevel/download/loglevel-1.8.1.tgz#5c621f83d5b48c54ae93b6156353f555963377b4"
  integrity sha1-XGIfg9W0jFSuk7YVY1P1VZYzd7Q=

long@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/long/download/long-4.0.0.tgz#9a7b71cfb7d361a194ea555241c92f7468d5bf28"
  integrity sha1-mntxz7fTYaGU6lVSQckvdGjVvyg=

loose-envify@^1.4.0:
  version "1.4.0"
  resolved "http://npm.htsc/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^4.1.3:
  version "4.1.5"
  resolved "http://npm.htsc/lru-cache/download/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "http://npm.htsc/lru-cache/download/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

lru-cache@^7.10.1, lru-cache@^7.14.1:
  version "7.18.3"
  resolved "http://npm.htsc/lru-cache/download/lru-cache-7.18.3.tgz#f793896e0fd0e954a59dfdd82f0773808df6aa89"
  integrity sha1-95OJbg/Q6VSlnf3YLwdzgI32qok=

macos-release@^2.5.0:
  version "2.5.0"
  resolved "http://npm.htsc/macos-release/download/macos-release-2.5.0.tgz#067c2c88b5f3fb3c56a375b2ec93826220fa1ff2"
  integrity sha1-BnwsiLXz+zxWo3Wy7JOCYiD6H/I=

magic-string@0.26.1:
  version "0.26.1"
  resolved "http://npm.htsc/magic-string/download/magic-string-0.26.1.tgz#ba9b651354fa9512474199acecf9c6dbe93f97fd"
  integrity sha1-uptlE1T6lRJHQZms7PnG2+k/l/0=
  dependencies:
    sourcemap-codec "^1.4.8"

magic-string@0.30.8:
  version "0.30.8"
  resolved "http://registry.npm.htsc/magic-string/-/magic-string-0.30.8.tgz#14e8624246d2bedba70d5462aa99ac9681844613"
  integrity sha512-ISQTe55T2ao7XtlAStud6qwYPZjE4GK1S/BeVPus4jrq6JuOnQ00YKQC581RWhR122W7msZV263KzVeLoqidyQ==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"

make-dir@^3.0.0:
  version "3.1.0"
  resolved "http://npm.htsc/make-dir/download/make-dir-3.1.0.tgz#415e967046b3a7f1d185277d84aa58203726a13f"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

make-error@1.x, make-error@^1.1.1:
  version "1.3.6"
  resolved "http://npm.htsc/make-error/download/make-error-1.3.6.tgz#2eb2e37ea9b67c4891f684a1394799af484cf7a2"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

makeerror@1.0.12:
  version "1.0.12"
  resolved "http://npm.htsc/makeerror/download/makeerror-1.0.12.tgz#3e5dd2079a82e812e983cc6610c4a2cb0eaa801a"
  integrity sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=
  dependencies:
    tmpl "1.0.5"

map-obj@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/map-obj/download/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-obj@^4.0.0:
  version "4.3.0"
  resolved "http://npm.htsc/map-obj/download/map-obj-4.3.0.tgz#9304f906e93faae70880da102a9f1df0ea8bb05a"
  integrity sha1-kwT5Buk/qucIgNoQKp8d8OqLsFo=

mathml-tag-names@^2.1.3:
  version "2.1.3"
  resolved "http://npm.htsc/mathml-tag-names/download/mathml-tag-names-2.1.3.tgz#4ddadd67308e780cf16a47685878ee27b736a0a3"
  integrity sha1-TdrdZzCOeAzxakdoWHjuJ7c2oKM=

mdast-util-from-markdown@^0.8.5:
  version "0.8.5"
  resolved "http://npm.htsc/mdast-util-from-markdown/download/mdast-util-from-markdown-0.8.5.tgz#d1ef2ca42bc377ecb0463a987910dae89bd9a28c"
  integrity sha1-0e8spCvDd+ywRjqYeRDa6JvZoow=
  dependencies:
    "@types/mdast" "^3.0.0"
    mdast-util-to-string "^2.0.0"
    micromark "~2.11.0"
    parse-entities "^2.0.0"
    unist-util-stringify-position "^2.0.0"

mdast-util-to-string@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/mdast-util-to-string/download/mdast-util-to-string-2.0.0.tgz#b8cfe6a713e1091cb5b728fc48885a4767f8b97b"
  integrity sha1-uM/mpxPhCRy1tyj8SIhaR2f4uXs=

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://npm.htsc/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memfs@^3.4.1:
  version "3.4.7"
  resolved "http://npm.htsc/memfs/download/memfs-3.4.7.tgz#e5252ad2242a724f938cb937e3c4f7ceb1f70e5a"
  integrity sha1-5SUq0iQqck+TjLk348T3zrH3Dlo=
  dependencies:
    fs-monkey "^1.0.3"

memory-fs@^0.2.0:
  version "0.2.0"
  resolved "http://npm.htsc/memory-fs/download/memory-fs-0.2.0.tgz#f2bb25368bc121e391c2520de92969caee0a0290"
  integrity sha1-8rslNovBIeORwlIN6Slpyu4KApA=

meow@^9.0.0:
  version "9.0.0"
  resolved "http://npm.htsc/meow/download/meow-9.0.0.tgz#cd9510bc5cac9dee7d03c73ee1f9ad959f4ea364"
  integrity sha1-zZUQvFysne59A8c+4fmtlZ9Oo2Q=
  dependencies:
    "@types/minimist" "^1.2.0"
    camelcase-keys "^6.2.2"
    decamelize "^1.2.0"
    decamelize-keys "^1.1.0"
    hard-rejection "^2.1.0"
    minimist-options "4.1.0"
    normalize-package-data "^3.0.0"
    read-pkg-up "^7.0.1"
    redent "^3.0.0"
    trim-newlines "^3.0.0"
    type-fest "^0.18.0"
    yargs-parser "^20.2.3"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/merge-descriptors/download/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "http://npm.htsc/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@^1.1.2, methods@~1.1.2:
  version "1.1.2"
  resolved "http://npm.htsc/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromark@~2.11.0:
  version "2.11.4"
  resolved "http://npm.htsc/micromark/download/micromark-2.11.4.tgz#d13436138eea826383e822449c9a5c50ee44665a"
  integrity sha1-0TQ2E47qgmOD6CJEnJpcUO5EZlo=
  dependencies:
    debug "^4.0.0"
    parse-entities "^2.0.0"

micromatch@^4.0.0, micromatch@^4.0.4, micromatch@^4.0.5:
  version "4.0.5"
  resolved "http://npm.htsc/micromatch/download/micromatch-4.0.5.tgz#bc8999a7cbbf77cdc89f132f6e467051b49090c6"
  integrity sha1-vImZp8u/d83InxMvbkZwUbSQkMY=
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://npm.htsc/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-types@^2.1.12, mime-types@^2.1.27, mime-types@~2.1.19, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "http://npm.htsc/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime@1.6.0:
  version "1.6.0"
  resolved "http://npm.htsc/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@2.6.0:
  version "2.6.0"
  resolved "http://npm.htsc/mime/download/mime-2.6.0.tgz#a2a682a95cd4d0cb1d6257e28f83da7e35800367"
  integrity sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

min-indent@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/min-indent/download/min-indent-1.0.1.tgz#a63f681673b30571fbe8bc25686ae746eefa9869"
  integrity sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=

minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "http://npm.htsc/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1, minimatch@^5.1.0:
  version "5.1.0"
  resolved "http://npm.htsc/minimatch/download/minimatch-5.1.0.tgz#1717b464f4971b144f6aabe8f2d0b8e4511e09c7"
  integrity sha1-Fxe0ZPSXGxRPaqvo8tC45FEeCcc=
  dependencies:
    brace-expansion "^2.0.1"

minimist-options@4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/minimist-options/download/minimist-options-4.1.0.tgz#c0655713c53a8a2ebd77ffa247d342c40f010619"
  integrity sha1-wGVXE8U6ii69d/+iR9NCxA8BBhk=
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"
    kind-of "^6.0.3"

minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.7"
  resolved "http://npm.htsc/minimist/download/minimist-1.2.7.tgz#daa1c4d91f507390437c6a8bc01078e7000c4d18"
  integrity sha1-2qHE2R9Qc5BDfGqLwBB45wAMTRg=

mkdirp@^0.5.1, mkdirp@^0.5.4:
  version "0.5.6"
  resolved "http://npm.htsc/mkdirp/download/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=
  dependencies:
    minimist "^1.2.6"

mkdirp@^1.0.4:
  version "1.0.4"
  resolved "http://npm.htsc/mkdirp/download/mkdirp-1.0.4.tgz#3eb5ed62622756d79a5f0e2a221dfebad75c2f7e"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

ms@2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "http://npm.htsc/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@2.1.3, ms@^2.1.1:
  version "2.1.3"
  resolved "http://npm.htsc/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

multer@1.4.4-lts.1:
  version "1.4.4-lts.1"
  resolved "http://npm.htsc/multer/download/multer-1.4.4-lts.1.tgz#24100f701a4611211cfae94ae16ea39bb314e04d"
  integrity sha1-JBAPcBpGESEc+ulK4W6jm7MU4E0=
  dependencies:
    append-field "^1.0.0"
    busboy "^1.0.0"
    concat-stream "^1.5.2"
    mkdirp "^0.5.4"
    object-assign "^4.1.1"
    type-is "^1.6.4"
    xtend "^4.0.0"

mute-stream@0.0.8:
  version "0.0.8"
  resolved "http://npm.htsc/mute-stream/download/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

mute-stream@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.htsc/mute-stream/-/mute-stream-1.0.0.tgz#e31bd9fe62f0aed23520aa4324ea6671531e013e"
  integrity sha512-avsJQhyd+680gKXyG/sQc0nXaC6rBkPOfyHYcFb9+hdkqQkR9bdnkJ0AMZhke0oesPqIO+mFFJ+IdBc7mst4IA==

mysql2@2.3.3:
  version "2.3.3"
  resolved "http://npm.htsc/mysql2/download/mysql2-2.3.3.tgz#944f3deca4b16629052ff8614fbf89d5552545a0"
  integrity sha1-lE897KSxZikFL/hhT7+J1VUlRaA=
  dependencies:
    denque "^2.0.1"
    generate-function "^2.3.1"
    iconv-lite "^0.6.3"
    long "^4.0.0"
    lru-cache "^6.0.0"
    named-placeholders "^1.1.2"
    seq-queue "^0.0.5"
    sqlstring "^2.3.2"

mz@^2.4.0:
  version "2.7.0"
  resolved "http://npm.htsc/mz/download/mz-2.7.0.tgz#95008057a56cafadc2bc63dde7f9ff6955948e32"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

named-placeholders@^1.1.2:
  version "1.1.2"
  resolved "http://npm.htsc/named-placeholders/download/named-placeholders-1.1.2.tgz#ceb1fbff50b6b33492b5cf214ccf5e39cef3d0e8"
  integrity sha1-zrH7/1C2szSStc8hTM9eOc7z0Og=
  dependencies:
    lru-cache "^4.1.3"

nanoid@^3.3.4:
  version "3.3.4"
  resolved "http://npm.htsc/nanoid/download/nanoid-3.3.4.tgz#730b67e3cd09e2deacf03c027c81c9d9dbc5e8ab"
  integrity sha1-cwtn480J4t6s8DwCfIHJ2dvF6Ks=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://npm.htsc/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.3, negotiator@^0.6.3:
  version "0.6.3"
  resolved "http://npm.htsc/negotiator/download/negotiator-0.6.3.tgz#58e323a72fedc0d6f9cd4d31fe49f51479590ccd"
  integrity sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=

neo-async@^2.6.2:
  version "2.6.2"
  resolved "http://npm.htsc/neo-async/download/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nest-router@1.0.9:
  version "1.0.9"
  resolved "http://npm.htsc/nest-router/download/nest-router-1.0.9.tgz#cbe814dadf90b765e0a5b77c562479d5d523a485"
  integrity sha1-y+gU2t+Qt2Xgpbd8ViR51dUjpIU=

nest-winston@^1.7.0:
  version "1.7.0"
  resolved "http://npm.htsc/nest-winston/download/nest-winston-1.7.0.tgz#92fd415d42c882e1d07201fb962140e5ba291c4c"
  integrity sha1-kv1BXULIguHQcgH7liFA5bopHEw=
  dependencies:
    fast-safe-stringify "^2.1.1"

nestjs-ctrip-apollo-client@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/nestjs-ctrip-apollo-client/download/nestjs-ctrip-apollo-client-1.0.1.tgz#832264414a8c28356d9a986d621b88b03d8766d8"
  integrity sha1-gyJkQUqMKDVtmphtYhuIsD2HZtg=
  dependencies:
    bluebird "^3.7.2"
    lodash "^4.17.15"
    request "^2.88.0"

nestjs-request-context@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.htsc/nestjs-request-context/-/nestjs-request-context-3.0.0.tgz#a6a709a2c9d2847ac3d5fd21ca00a7551a56fd69"
  integrity sha512-S7MvH/ouOnaVEzHQxplGnPXNcOsp0FfMQSm6wFzsrMF4P5Ca3Okz0dgZpg4Gul7D4w2NhC8fFPsr/1i/3gFkfQ==

nestjs-typeorm-paginate@4.0.2:
  version "4.0.2"
  resolved "http://npm.htsc/nestjs-typeorm-paginate/download/nestjs-typeorm-paginate-4.0.2.tgz#8c4bb25d801574210832db0c8292318e5b9beee7"
  integrity sha1-jEuyXYAVdCEIMtsMgpIxjlub7uc=

node-abort-controller@^3.1.1:
  version "3.1.1"
  resolved "http://npm.htsc/node-abort-controller/download/node-abort-controller-3.1.1.tgz#a94377e964a9a37ac3976d848cb5c765833b8548"
  integrity sha1-qUN36WSpo3rDl22EjLXHZYM7hUg=

node-emoji@1.11.0:
  version "1.11.0"
  resolved "http://npm.htsc/node-emoji/download/node-emoji-1.11.0.tgz#69a0150e6946e2f115e9d7ea4df7971e2628301c"
  integrity sha1-aaAVDmlG4vEV6dfqTfeXHiYoMBw=
  dependencies:
    lodash "^4.17.21"

node-fetch@2, node-fetch@^2.6.1:
  version "2.6.7"
  resolved "http://npm.htsc/node-fetch/download/node-fetch-2.6.7.tgz#24de9fba827e3b4ae44dc8b20256a379160052ad"
  integrity sha1-JN6fuoJ+O0rkTciyAlajeRYAUq0=
  dependencies:
    whatwg-url "^5.0.0"

node-fetch@^2.6.7:
  version "2.6.11"
  resolved "http://npm.htsc/node-fetch/download/node-fetch-2.6.11.tgz#cde7fc71deef3131ef80a738919f999e6edfff25"
  integrity sha1-zef8cd7vMTHvgKc4kZ+Znm7f/yU=
  dependencies:
    whatwg-url "^5.0.0"

node-int64@^0.4.0:
  version "0.4.0"
  resolved "http://npm.htsc/node-int64/download/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-releases@^2.0.6:
  version "2.0.6"
  resolved "http://npm.htsc/node-releases/download/node-releases-2.0.6.tgz#8a7088c63a55e493845683ebf3c828d8c51c5503"
  integrity sha1-inCIxjpV5JOEVoPr88go2MUcVQM=

normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "http://npm.htsc/normalize-package-data/download/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-package-data@^3.0.0:
  version "3.0.3"
  resolved "http://npm.htsc/normalize-package-data/download/normalize-package-data-3.0.3.tgz#dbcc3e2da59509a0983422884cd172eefdfa525e"
  integrity sha1-28w+LaWVCaCYNCKITNFy7v36Ul4=
  dependencies:
    hosted-git-info "^4.0.1"
    is-core-module "^2.5.0"
    semver "^7.3.4"
    validate-npm-package-license "^3.0.1"

normalize-path@3.0.0, normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-run-path@^4.0.0, npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "http://npm.htsc/npm-run-path/download/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  resolved "http://npm.htsc/nth-check/download/nth-check-2.1.1.tgz#c9eab428effce36cd6b92c924bdb000ef1f1ed1d"
  integrity sha1-yeq0KO/842zWuSySS9sADvHx7R0=
  dependencies:
    boolbase "^1.0.0"

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "http://npm.htsc/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4, object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://npm.htsc/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-inspect@^1.12.2, object-inspect@^1.9.0:
  version "1.12.2"
  resolved "http://npm.htsc/object-inspect/download/object-inspect-1.12.2.tgz#c0641f26394532f28ab8d796ab954e43c009a8ea"
  integrity sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo=

object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object.assign@^4.1.0, object.assign@^4.1.2, object.assign@^4.1.3, object.assign@^4.1.4:
  version "4.1.4"
  resolved "http://npm.htsc/object.assign/download/object.assign-4.1.4.tgz#9673c7c7c351ab8c4d0b516f4343ebf4dfb7799f"
  integrity sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.entries@^1.1.2, object.entries@^1.1.5:
  version "1.1.5"
  resolved "http://npm.htsc/object.entries/download/object.entries-1.1.5.tgz#e1acdd17c4de2cd96d5a08487cfb9db84d881861"
  integrity sha1-4azdF8TeLNltWghIfPuduE2IGGE=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

object.fromentries@^2.0.5:
  version "2.0.5"
  resolved "http://npm.htsc/object.fromentries/download/object.fromentries-2.0.5.tgz#7b37b205109c21e741e605727fe8b0ad5fa08251"
  integrity sha1-ezeyBRCcIedB5gVyf+iwrV+gglE=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

object.hasown@^1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/object.hasown/download/object.hasown-1.1.1.tgz#ad1eecc60d03f49460600430d97f23882cf592a3"
  integrity sha1-rR7sxg0D9JRgYAQw2X8jiCz1kqM=
  dependencies:
    define-properties "^1.1.4"
    es-abstract "^1.19.5"

object.values@^1.1.5:
  version "1.1.5"
  resolved "http://npm.htsc/object.values/download/object.values-1.1.5.tgz#959f63e3ce9ef108720333082131e4a459b716ac"
  integrity sha1-lZ9j486e8QhyAzMIITHkpFm3Fqw=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

on-finished@2.4.1:
  version "2.4.1"
  resolved "http://npm.htsc/on-finished/download/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

once@1.4.0, once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://npm.htsc/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

one-time@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/one-time/download/one-time-1.0.0.tgz#e06bc174aed214ed58edede573b433bbf827cb45"
  integrity sha1-4GvBdK7SFO1Y7e3lc7Qzu/gny0U=
  dependencies:
    fn.name "1.x.x"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "http://npm.htsc/onetime/download/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

optionator@^0.9.1:
  version "0.9.1"
  resolved "http://npm.htsc/optionator/download/optionator-0.9.1.tgz#4f236a6373dae0566a6d43e1326674f50c291499"
  integrity sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.3"

ora@5.4.1, ora@^5.4.1:
  version "5.4.1"
  resolved "http://npm.htsc/ora/download/ora-5.4.1.tgz#1b2678426af4ac4a509008e5e4ac9e9959db9e18"
  integrity sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

os-name@4.0.1:
  version "4.0.1"
  resolved "http://npm.htsc/os-name/download/os-name-4.0.1.tgz#32cee7823de85a8897647ba4d76db46bf845e555"
  integrity sha1-Ms7ngj3oWoiXZHuk1220a/hF5VU=
  dependencies:
    macos-release "^2.5.0"
    windows-release "^4.0.0"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-limit@^2.2.0:
  version "2.3.0"
  resolved "http://npm.htsc/p-limit/download/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.1.0:
  version "3.1.0"
  resolved "http://npm.htsc/p-limit/download/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://npm.htsc/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

packet-reader@1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/packet-reader/download/packet-reader-1.0.0.tgz#9238e5480dedabacfe1fe3f2771063f164157d74"
  integrity sha1-kjjlSA3tq6z+H+PydxBj8WQVfXQ=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-entities@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/parse-entities/download/parse-entities-2.0.0.tgz#53c6eb5b9314a1f4ec99fa0fdf7ce01ecda0cbe8"
  integrity sha1-U8brW5MUofTsmfoP33zgHs2gy+g=
  dependencies:
    character-entities "^1.0.0"
    character-entities-legacy "^1.0.0"
    character-reference-invalid "^1.0.0"
    is-alphanumerical "^1.0.0"
    is-decimal "^1.0.0"
    is-hexadecimal "^1.0.0"

parse-json@^5.0.0, parse-json@^5.2.0:
  version "5.2.0"
  resolved "http://npm.htsc/parse-json/download/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5-htmlparser2-tree-adapter@^6.0.0:
  version "6.0.1"
  resolved "http://npm.htsc/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-6.0.1.tgz#2cdf9ad823321140370d4dbf5d3e92c7c8ddc6e6"
  integrity sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY=
  dependencies:
    parse5 "^6.0.1"

parse5@^5.1.1:
  version "5.1.1"
  resolved "http://npm.htsc/parse5/download/parse5-5.1.1.tgz#f68e4e5ba1852ac2cadc00f4555fff6c2abb6178"
  integrity sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=

parse5@^6.0.1:
  version "6.0.1"
  resolved "http://npm.htsc/parse5/download/parse5-6.0.1.tgz#e1a1c085c569b3dc08321184f19a39cc27f7c30b"
  integrity sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=

parseurl@~1.3.3:
  version "1.3.3"
  resolved "http://npm.htsc/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

passport-jwt@4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/passport-jwt/download/passport-jwt-4.0.0.tgz#7f0be7ba942e28b9f5d22c2ebbb8ce96ef7cf065"
  integrity sha1-fwvnupQuKLn10iwuu7jOlu988GU=
  dependencies:
    jsonwebtoken "^8.2.0"
    passport-strategy "^1.0.0"

passport-strategy@1.x.x, passport-strategy@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/passport-strategy/download/passport-strategy-1.0.0.tgz#b5539aa8fc225a3d1ad179476ddf236b440f52e4"
  integrity sha1-tVOaqPwiWj0a0XlHbd8ja0QPUuQ=

passport@0.6.0:
  version "0.6.0"
  resolved "http://npm.htsc/passport/download/passport-0.6.0.tgz#e869579fab465b5c0b291e841e6cc95c005fac9d"
  integrity sha1-6GlXn6tGW1wLKR6EHmzJXABfrJ0=
  dependencies:
    passport-strategy "1.x.x"
    pause "0.0.1"
    utils-merge "^1.0.1"

path-browserify@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/path-browserify/download/path-browserify-1.0.1.tgz#d98454a9c3753d5790860f16f68867b9e46be1fd"
  integrity sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://npm.htsc/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "http://npm.htsc/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://npm.htsc/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "http://npm.htsc/path-to-regexp/download/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-to-regexp@3.2.0:
  version "3.2.0"
  resolved "http://npm.htsc/path-to-regexp/download/path-to-regexp-3.2.0.tgz#fa7877ecbc495c601907562222453c43cc204a5f"
  integrity sha1-+nh37LxJXGAZB1YiIkU8Q8wgSl8=

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pause@0.0.1:
  version "0.0.1"
  resolved "http://npm.htsc/pause/download/pause-0.0.1.tgz#1d408b3fdb76923b9543d96fb4c9dfd535d9cb5d"
  integrity sha1-HUCLP9t2kjuVQ9lvtMnf1TXZy10=

pend@~1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/pend/download/pend-1.2.0.tgz#7a57eb550a6783f9115331fcf4663d5c8e007a50"
  integrity sha1-elfrVQpng/kRUzH89GY9XI4AelA=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

pg-connection-string@^2.5.0:
  version "2.5.0"
  resolved "http://npm.htsc/pg-connection-string/download/pg-connection-string-2.5.0.tgz#538cadd0f7e603fc09a12590f3b8a452c2c0cf34"
  integrity sha1-U4yt0PfmA/wJoSWQ87ikUsLAzzQ=

pg-int8@1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/pg-int8/download/pg-int8-1.0.1.tgz#943bd463bf5b71b4170115f80f8efc9a0c0eb78c"
  integrity sha1-lDvUY79bcbQXARX4D478mgwOt4w=

pg-pool@^3.5.2:
  version "3.5.2"
  resolved "http://npm.htsc/pg-pool/download/pg-pool-3.5.2.tgz#ed1bed1fb8d79f1c6fd5fb1c99e990fbf9ddf178"
  integrity sha1-7RvtH7jXnxxv1fscmemQ+/nd8Xg=

pg-protocol@^1.5.0:
  version "1.5.0"
  resolved "http://npm.htsc/pg-protocol/download/pg-protocol-1.5.0.tgz#b5dd452257314565e2d54ab3c132adc46565a6a0"
  integrity sha1-td1FIlcxRWXi1UqzwTKtxGVlpqA=

pg-types@^2.1.0:
  version "2.2.0"
  resolved "http://npm.htsc/pg-types/download/pg-types-2.2.0.tgz#2d0250d636454f7cfa3b6ae0382fdfa8063254a3"
  integrity sha1-LQJQ1jZFT3z6O2rgOC/fqAYyVKM=
  dependencies:
    pg-int8 "1.0.1"
    postgres-array "~2.0.0"
    postgres-bytea "~1.0.0"
    postgres-date "~1.0.4"
    postgres-interval "^1.1.0"

pg@^8.8.0:
  version "8.8.0"
  resolved "http://npm.htsc/pg/download/pg-8.8.0.tgz#a77f41f9d9ede7009abfca54667c775a240da686"
  integrity sha1-p39B+dnt5wCav8pUZnx3WiQNpoY=
  dependencies:
    buffer-writer "2.0.0"
    packet-reader "1.0.0"
    pg-connection-string "^2.5.0"
    pg-pool "^3.5.2"
    pg-protocol "^1.5.0"
    pg-types "^2.1.0"
    pgpass "1.x"

pgpass@1.x:
  version "1.0.5"
  resolved "http://npm.htsc/pgpass/download/pgpass-1.0.5.tgz#9b873e4a564bb10fa7a7dbd55312728d422a223d"
  integrity sha1-m4c+SlZLsQ+np9vVUxJyjUIqIj0=
  dependencies:
    split2 "^4.1.0"

picocolors@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/picocolors/download/picocolors-1.0.0.tgz#cb5bdc74ff3f51892236eaf79d68bc44564ab81c"
  integrity sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=

picomatch@4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.htsc/picomatch/-/picomatch-4.0.1.tgz#68c26c8837399e5819edce48590412ea07f17a07"
  integrity sha512-xUXwsxNjwTQ8K3GnT4pCJm+xq3RUPQbmkYJTP5aFIfNIvbcc/4MUxgBaaRSZJ6yGJZiGSyYlM6MzwTsRk8SYCg==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.3, picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://npm.htsc/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

pirates@^4.0.4:
  version "4.0.5"
  resolved "http://npm.htsc/pirates/download/pirates-4.0.5.tgz#feec352ea5c3268fb23a37c702ab1699f35a5f3b"
  integrity sha1-/uw1LqXDJo+yOjfHAqsWmfNaXzs=

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "http://npm.htsc/pkg-dir/download/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

pluralize@8.0.0:
  version "8.0.0"
  resolved "http://npm.htsc/pluralize/download/pluralize-8.0.0.tgz#1a6fa16a38d12a1901e0320fa017051c539ce3b1"
  integrity sha1-Gm+hajjRKhkB4DIPoBcFHFOc47E=

postcss-html@^1.5.0:
  version "1.5.0"
  resolved "http://npm.htsc/postcss-html/download/postcss-html-1.5.0.tgz#57a43bc9e336f516ecc448a37d2e8c2290170a6f"
  integrity sha1-V6Q7yeM29RbsxEijfS6MIpAXCm8=
  dependencies:
    htmlparser2 "^8.0.0"
    js-tokens "^8.0.0"
    postcss "^8.4.0"
    postcss-safe-parser "^6.0.0"

postcss-less@^6.0.0:
  version "6.0.0"
  resolved "http://npm.htsc/postcss-less/download/postcss-less-6.0.0.tgz#463b34c60f53b648c237f569aeb2e09149d85af4"
  integrity sha1-Rjs0xg9TtkjCN/VprrLgkUnYWvQ=

postcss-media-query-parser@^0.2.3:
  version "0.2.3"
  resolved "http://npm.htsc/postcss-media-query-parser/download/postcss-media-query-parser-0.2.3.tgz#27b39c6f4d94f81b1a73b8f76351c609e5cef244"
  integrity sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=

postcss-resolve-nested-selector@^0.1.1:
  version "0.1.1"
  resolved "http://npm.htsc/postcss-resolve-nested-selector/download/postcss-resolve-nested-selector-0.1.1.tgz#29ccbc7c37dedfac304e9fff0bf1596b3f6a0e4e"
  integrity sha1-Kcy8fDfe36wwTp//C/FZaz9qDk4=

postcss-safe-parser@^6.0.0:
  version "6.0.0"
  resolved "http://npm.htsc/postcss-safe-parser/download/postcss-safe-parser-6.0.0.tgz#bb4c29894171a94bc5c996b9a30317ef402adaa1"
  integrity sha1-u0wpiUFxqUvFyZa5owMX70Aq2qE=

postcss-selector-parser@^6.0.10, postcss-selector-parser@^6.0.6, postcss-selector-parser@^6.0.9:
  version "6.0.10"
  resolved "http://npm.htsc/postcss-selector-parser/download/postcss-selector-parser-6.0.10.tgz#79b61e2c0d1bfc2602d549e11d0876256f8df88d"
  integrity sha1-ebYeLA0b/CYC1UnhHQh2JW+N+I0=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "http://npm.htsc/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz#723c09920836ba6d3e5af019f92bc0971c02e514"
  integrity sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=

postcss@^8.4.0, postcss@^8.4.16, postcss@^8.4.17:
  version "8.4.18"
  resolved "http://npm.htsc/postcss/download/postcss-8.4.18.tgz#6d50046ea7d3d66a85e0e782074e7203bc7fbca2"
  integrity sha1-bVAEbqfT1mqF4OeCB05yA7x/vKI=
  dependencies:
    nanoid "^3.3.4"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

postcss@^8.4.19:
  version "8.4.19"
  resolved "http://npm.htsc/postcss/download/postcss-8.4.19.tgz#61178e2add236b17351897c8bcc0b4c8ecab56fc"
  integrity sha1-YReOKt0jaxc1GJfIvMC0yOyrVvw=
  dependencies:
    nanoid "^3.3.4"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

postgres-array@~2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/postgres-array/download/postgres-array-2.0.0.tgz#48f8fce054fbc69671999329b8834b772652d82e"
  integrity sha1-SPj84FT7xpZxmZMpuINLdyZS2C4=

postgres-bytea@~1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/postgres-bytea/download/postgres-bytea-1.0.0.tgz#027b533c0aa890e26d172d47cf9ccecc521acd35"
  integrity sha1-AntTPAqokOJtFy1Hz5zOzFIazTU=

postgres-date@~1.0.4:
  version "1.0.7"
  resolved "http://npm.htsc/postgres-date/download/postgres-date-1.0.7.tgz#51bc086006005e5061c591cee727f2531bf641a8"
  integrity sha1-UbwIYAYAXlBhxZHO5yfyUxv2Qag=

postgres-interval@^1.1.0:
  version "1.2.0"
  resolved "http://npm.htsc/postgres-interval/download/postgres-interval-1.2.0.tgz#b460c82cb1587507788819a06aa0fffdb3544695"
  integrity sha1-tGDILLFYdQd4iBmgaqD//bNURpU=
  dependencies:
    xtend "^4.0.0"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://npm.htsc/prelude-ls/download/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.7.1:
  version "2.7.1"
  resolved "http://npm.htsc/prettier/download/prettier-2.7.1.tgz#e235806850d057f97bb08368a4f7d899f7760c64"
  integrity sha1-4jWAaFDQV/l7sINopPfYmfd2DGQ=

pretty-format@^28.0.0, pretty-format@^28.1.3:
  version "28.1.3"
  resolved "http://npm.htsc/pretty-format/download/pretty-format-28.1.3.tgz#c9fba8cedf99ce50963a11b27d982a9ae90970d5"
  integrity sha1-yfuozt+ZzlCWOhGyfZgqmukJcNU=
  dependencies:
    "@jest/schemas" "^28.1.3"
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://npm.htsc/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

progress@^2.0.0:
  version "2.0.3"
  resolved "http://npm.htsc/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

prompts@^2.0.1:
  version "2.4.2"
  resolved "http://npm.htsc/prompts/download/prompts-2.4.2.tgz#7b57e73b3a48029ad10ebd44f74b01722a4cb069"
  integrity sha1-e1fnOzpIAprRDr1E90sBcipMsGk=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@^15.8.1:
  version "15.8.1"
  resolved "http://npm.htsc/prop-types/download/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

protobufjs@^6.11.2:
  version "6.11.3"
  resolved "http://npm.htsc/protobufjs/download/protobufjs-6.11.3.tgz#637a527205a35caa4f3e2a9a4a13ddffe0e7af74"
  integrity sha1-Y3pScgWjXKpPPiqaShPd/+Dnr3Q=
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/long" "^4.0.1"
    "@types/node" ">=13.7.0"
    long "^4.0.0"

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "http://npm.htsc/proxy-addr/download/proxy-addr-2.0.7.tgz#f19fe69ceab311eeb94b42e70e8c2070f9ba1025"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.htsc/proxy-from-env/-/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/pseudomap/download/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28:
  version "1.9.0"
  resolved "http://npm.htsc/psl/download/psl-1.9.0.tgz#d0df2a137f00794565fcaf3b2c00cd09f8d5a5a7"
  integrity sha1-0N8qE38AeUVl/K87LADNCfjVpac=

pump@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "http://npm.htsc/punycode/download/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

qs@6.10.3:
  version "6.10.3"
  resolved "http://npm.htsc/qs/download/qs-6.10.3.tgz#d6cde1b2ffca87b5aa57889816c5f81535e22e8e"
  integrity sha1-1s3hsv/Kh7WqV4iYFsX4FTXiLo4=
  dependencies:
    side-channel "^1.0.4"

qs@6.11.0, qs@^6.11.0:
  version "6.11.0"
  resolved "http://npm.htsc/qs/download/qs-6.11.0.tgz#fd0d963446f7a65e1367e01abd85429453f0c37a"
  integrity sha1-/Q2WNEb3pl4TZ+AavYVClFPww3o=
  dependencies:
    side-channel "^1.0.4"

qs@6.9.3:
  version "6.9.3"
  resolved "http://npm.htsc/qs/download/qs-6.9.3.tgz#bfadcd296c2d549f1dffa560619132c977f5008e"
  integrity sha1-v63NKWwtVJ8d/6VgYZEyyXf1AI4=

qs@~6.5.2:
  version "6.5.3"
  resolved "http://npm.htsc/qs/download/qs-6.5.3.tgz#3aeeffc91967ef6e35c0e488ef46fb296ab76aad"
  integrity sha1-Ou7/yRln7241wOSI70b7KWq3aq0=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://npm.htsc/queue-microtask/download/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

quick-lru@^4.0.1:
  version "4.0.1"
  resolved "http://npm.htsc/quick-lru/download/quick-lru-4.0.1.tgz#5b8878f113a58217848c6482026c73e1ba57727f"
  integrity sha1-W4h48ROlgheEjGSCAmxz4bpXcn8=

randombytes@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/randombytes/download/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

range-parser@~1.2.1:
  version "1.2.1"
  resolved "http://npm.htsc/range-parser/download/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.5.1:
  version "2.5.1"
  resolved "http://npm.htsc/raw-body/download/raw-body-2.5.1.tgz#fe1b1628b181b700215e5fd42389f98b71392857"
  integrity sha1-/hsWKLGBtwAhXl/UI4n5i3E5KFc=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

raw-body@2.5.2:
  version "2.5.2"
  resolved "http://npm.htsc/raw-body/download/raw-body-2.5.2.tgz#99febd83b90e08975087e8f1f9419a149366b68a"
  integrity sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

react-is@^16.13.1:
  version "16.13.1"
  resolved "http://npm.htsc/react-is/download/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^18.0.0:
  version "18.2.0"
  resolved "http://npm.htsc/react-is/download/react-is-18.2.0.tgz#199431eeaaa2e09f86427efbb4f1473edb47609b"
  integrity sha1-GZQx7qqi4J+GQn77tPFHPttHYJs=

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "http://npm.htsc/read-pkg-up/download/read-pkg-up-7.0.1.tgz#f3a6135758459733ae2b95638056e1854e7ef507"
  integrity sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^5.2.0:
  version "5.2.0"
  resolved "http://npm.htsc/read-pkg/download/read-pkg-5.2.0.tgz#7bf295438ca5a33e56cd30e053b34ee7250c93cc"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

readable-stream@^2.2.2, readable-stream@^2.3.0, readable-stream@^2.3.5:
  version "2.3.7"
  resolved "http://npm.htsc/readable-stream/download/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.4.0, readable-stream@^3.6.0:
  version "3.6.0"
  resolved "http://npm.htsc/readable-stream/download/readable-stream-3.6.0.tgz#337bbda3adc0706bd3e024426a286d4b4b2c9198"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://npm.htsc/readdirp/download/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "http://npm.htsc/rechoir/download/rechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

redent@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/redent/download/redent-3.0.0.tgz#e557b7998316bb53c9f1f56fa626352c6963059f"
  integrity sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

redis-errors@^1.0.0, redis-errors@^1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/redis-errors/download/redis-errors-1.2.0.tgz#eb62d2adb15e4eaf4610c04afe1529384250abad"
  integrity sha1-62LSrbFeTq9GEMBK/hUpOEJQq60=

redis-parser@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/redis-parser/download/redis-parser-3.0.0.tgz#b66d828cdcafe6b4b8a428a7def4c6bcac31c8b4"
  integrity sha1-tm2CjNyv5rS4pCin3vTGvKwxyLQ=
  dependencies:
    redis-errors "^1.0.0"

reflect-metadata@0.1.13, reflect-metadata@^0.1.13:
  version "0.1.13"
  resolved "http://npm.htsc/reflect-metadata/download/reflect-metadata-0.1.13.tgz#67ae3ca57c972a2aa1642b10fe363fe32d49dc08"
  integrity sha1-Z648pXyXKiqhZCsQ/jY/4y1J3Ag=

regenerate-unicode-properties@^10.0.1:
  version "10.1.0"
  resolved "http://npm.htsc/regenerate-unicode-properties/download/regenerate-unicode-properties-10.1.0.tgz#7c3192cab6dd24e21cb4461e5ddd7dd24fa8374c"
  integrity sha1-fDGSyrbdJOIctEYeXd190k+oN0w=
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "http://npm.htsc/regenerate/download/regenerate-1.4.2.tgz#b9346d8827e8f5a32f7ba29637d398b69014848a"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.13.10, regenerator-runtime@^0.13.4:
  version "0.13.10"
  resolved "http://npm.htsc/regenerator-runtime/download/regenerator-runtime-0.13.10.tgz#ed07b19616bcbec5da6274ebc75ae95634bfc2ee"
  integrity sha1-7Qexlha8vsXaYnTrx1rpVjS/wu4=

regenerator-transform@^0.15.0:
  version "0.15.0"
  resolved "http://npm.htsc/regenerator-transform/download/regenerator-transform-0.15.0.tgz#cbd9ead5d77fae1a48d957cf889ad0586adb6537"
  integrity sha1-y9nq1dd/rhpI2VfPiJrQWGrbZTc=
  dependencies:
    "@babel/runtime" "^7.8.4"

regexp.prototype.flags@^1.4.1, regexp.prototype.flags@^1.4.3:
  version "1.4.3"
  resolved "http://npm.htsc/regexp.prototype.flags/download/regexp.prototype.flags-1.4.3.tgz#87cab30f80f66660181a3bb7bf5981a872b367ac"
  integrity sha1-h8qzD4D2ZmAYGju3v1mBqHKzZ6w=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    functions-have-names "^1.2.2"

regexpp@^3.0.0, regexpp@^3.1.0, regexpp@^3.2.0:
  version "3.2.0"
  resolved "http://npm.htsc/regexpp/download/regexpp-3.2.0.tgz#0425a2768d8f23bad70ca4b90461fa2f1213e1b2"
  integrity sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=

regexpu-core@^5.1.0:
  version "5.1.0"
  resolved "http://npm.htsc/regexpu-core/download/regexpu-core-5.1.0.tgz#2f8504c3fd0ebe11215783a41541e21c79942c6d"
  integrity sha1-L4UEw/0OvhEhV4OkFUHiHHmULG0=
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.0.1"
    regjsgen "^0.6.0"
    regjsparser "^0.8.2"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.0.0"

regjsgen@^0.6.0:
  version "0.6.0"
  resolved "http://npm.htsc/regjsgen/download/regjsgen-0.6.0.tgz#83414c5354afd7d6627b16af5f10f41c4e71808d"
  integrity sha1-g0FMU1Sv19ZiexavXxD0HE5xgI0=

regjsparser@^0.8.2:
  version "0.8.4"
  resolved "http://npm.htsc/regjsparser/download/regjsparser-0.8.4.tgz#8a14285ffcc5de78c5b95d62bbf413b6bc132d5f"
  integrity sha1-ihQoX/zF3njFuV1iu/QTtrwTLV8=
  dependencies:
    jsesc "~0.5.0"

request@^2.88.0:
  version "2.88.2"
  resolved "http://npm.htsc/request/download/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://npm.htsc/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "http://npm.htsc/require-from-string/download/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/resolve-cwd/download/resolve-cwd-3.0.0.tgz#0f0075f1bb2544766cf73ba6a6e2adfebcb13f2d"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "http://npm.htsc/resolve-from/download/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve.exports@^1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/resolve.exports/download/resolve.exports-1.1.0.tgz#5ce842b94b05146c0e03076985d1d0e7e48c90c9"
  integrity sha1-XOhCuUsFFGwOAwdphdHQ5+SMkMk=

resolve@^1.1.6, resolve@^1.10.0, resolve@^1.10.1, resolve@^1.14.2, resolve@^1.20.0, resolve@^1.22.0:
  version "1.22.1"
  resolved "http://npm.htsc/resolve/download/resolve-1.22.1.tgz#27cb2ebb53f91abb49470a928bba7558066ac177"
  integrity sha1-J8suu1P5GrtJRwqSi7p1WAZqwXc=
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.3:
  version "2.0.0-next.4"
  resolved "http://npm.htsc/resolve/download/resolve-2.0.0-next.4.tgz#3d37a113d6429f496ec4752d2a2e58efb1fd4660"
  integrity sha1-PTehE9ZCn0luxHUtKi5Y77H9RmA=
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://npm.htsc/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

retry@0.13.1:
  version "0.13.1"
  resolved "http://npm.htsc/retry/download/retry-0.13.1.tgz#185b1587acf67919d63b357349e03537b2484658"
  integrity sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=

reusify@^1.0.4:
  version "1.0.4"
  resolved "http://npm.htsc/reusify/download/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rimraf@3.0.2, rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://npm.htsc/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

run-async@^2.4.0:
  version "2.4.1"
  resolved "http://npm.htsc/run-async/download/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-async@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.htsc/run-async/-/run-async-3.0.0.tgz#42a432f6d76c689522058984384df28be379daad"
  integrity sha512-540WwVDOMxA6dN6We19EcT9sc3hkXPw5mzRNGM3FkdN/vtE9NFvj5lFAPNwUDmJjXidm3v7TC1cTE7t17Ulm1Q==

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://npm.htsc/run-parallel/download/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rxjs@6.6.7, rxjs@^6.6.0:
  version "6.6.7"
  resolved "http://npm.htsc/rxjs/download/rxjs-6.6.7.tgz#90ac018acabf491bf65044235d5863c4dab804c9"
  integrity sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=
  dependencies:
    tslib "^1.9.0"

rxjs@7.5.6:
  version "7.5.6"
  resolved "http://npm.htsc/rxjs/download/rxjs-7.5.6.tgz#0446577557862afd6903517ce7cae79ecb9662bc"
  integrity sha1-BEZXdVeGKv1pA1F858rnnsuWYrw=
  dependencies:
    tslib "^2.1.0"

rxjs@7.8.1, rxjs@^7.8.1:
  version "7.8.1"
  resolved "http://registry.npm.htsc/rxjs/-/rxjs-7.8.1.tgz#6f6f3d99ea8044291efd92e7c7fcf562c4057543"
  integrity sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==
  dependencies:
    tslib "^2.1.0"

rxjs@^7.5.5:
  version "7.5.7"
  resolved "http://npm.htsc/rxjs/download/rxjs-7.5.7.tgz#2ec0d57fdc89ece220d2e702730ae8f1e49def39"
  integrity sha1-LsDVf9yJ7OIg0ucCcwro8eSd7zk=
  dependencies:
    tslib "^2.1.0"

safe-buffer@5.2.1, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://npm.htsc/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://npm.htsc/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-regex-test@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/safe-regex-test/download/safe-regex-test-1.0.0.tgz#793b874d524eb3640d1873aad03596db2d4f2295"
  integrity sha1-eTuHTVJOs2QNGHOq0DWW2y1PIpU=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.3"
    is-regex "^1.1.4"

safe-stable-stringify@^2.3.1:
  version "2.4.1"
  resolved "http://npm.htsc/safe-stable-stringify/download/safe-stable-stringify-2.4.1.tgz#34694bd8a30575b7f94792aa51527551bd733d61"
  integrity sha1-NGlL2KMFdbf5R5KqUVJ1Ub1zPWE=

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "http://npm.htsc/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sax@>=0.6.0:
  version "1.2.4"
  resolved "http://npm.htsc/sax/download/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

schema-utils@^3.1.0, schema-utils@^3.1.1:
  version "3.1.1"
  resolved "http://npm.htsc/schema-utils/download/schema-utils-3.1.1.tgz#bc74c4b6b6995c1d88f76a8b77bea7219e0c8281"
  integrity sha1-vHTEtraZXB2I92qLd76nIZ4MgoE=
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

"semver@2 || 3 || 4 || 5", semver@^5.6.0, semver@^5.7.1:
  version "5.7.1"
  resolved "http://npm.htsc/semver/download/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@7.x, semver@^7.2.1, semver@^7.3.4, semver@^7.3.5, semver@^7.3.7, semver@^7.3.8:
  version "7.3.8"
  resolved "http://npm.htsc/semver/download/semver-7.3.8.tgz#07a78feafb3f7b32347d725e33de7e2a2df67798"
  integrity sha1-B6eP6vs/ezI0fXJeM95+Ki32d5g=
  dependencies:
    lru-cache "^6.0.0"

semver@^6.0.0, semver@^6.1.0, semver@^6.1.1, semver@^6.1.2, semver@^6.3.0:
  version "6.3.0"
  resolved "http://npm.htsc/semver/download/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

send@0.18.0:
  version "0.18.0"
  resolved "http://npm.htsc/send/download/send-0.18.0.tgz#670167cc654b05f5aa4a767f9113bb371bc706be"
  integrity sha1-ZwFnzGVLBfWqSnZ/kRO7NxvHBr4=
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

seq-queue@^0.0.5:
  version "0.0.5"
  resolved "http://npm.htsc/seq-queue/download/seq-queue-0.0.5.tgz#d56812e1c017a6e4e7c3e3a37a1da6d78dd3c93e"
  integrity sha1-1WgS4cAXpuTnw+Ojeh2m143TyT4=

serialize-javascript@^6.0.0:
  version "6.0.0"
  resolved "http://npm.htsc/serialize-javascript/download/serialize-javascript-6.0.0.tgz#efae5d88f45d7924141da8b5c3a7a7e663fefeb8"
  integrity sha1-765diPRdeSQUHai1w6en5mP+/rg=
  dependencies:
    randombytes "^2.1.0"

serve-static@1.15.0:
  version "1.15.0"
  resolved "http://npm.htsc/serve-static/download/serve-static-1.15.0.tgz#faaef08cffe0a1a62f60cad0c4e513cff0ac9540"
  integrity sha1-+q7wjP/goaYvYMrQxOUTz/CslUA=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "http://registry.npm.htsc/set-function-length/-/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "http://npm.htsc/setprototypeof/download/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

sha.js@^2.4.11:
  version "2.4.11"
  resolved "http://npm.htsc/sha.js/download/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shelljs@0.8.5:
  version "0.8.5"
  resolved "http://npm.htsc/shelljs/download/shelljs-0.8.5.tgz#de055408d8361bed66c669d2f000538ced8ee20c"
  integrity sha1-3gVUCNg2G+1mxmnS8ABTjO2O4gw=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

side-channel@^1.0.4:
  version "1.0.4"
  resolved "http://npm.htsc/side-channel/download/side-channel-1.0.4.tgz#efce5c8fdc104ee751b25c58d4290011fa5ea2cf"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.2, signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "http://npm.htsc/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "http://npm.htsc/simple-swizzle/download/simple-swizzle-0.2.2.tgz#a4da6b635ffcccca33f70d17cb92592de95e557a"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/sisteransi/download/sisteransi-1.0.5.tgz#134d681297756437cc05ca01370d3a7a571075ed"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/slice-ansi/download/slice-ansi-4.0.0.tgz#500e8dd0fd55b05815086255b3195adf2a45fe6b"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

source-map-js@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/source-map-js/download/source-map-js-1.0.2.tgz#adbc361d9c62df380125e7f161f71c826f1e490c"
  integrity sha1-rbw2HZxi3zgBJefxYfccgm8eSQw=

source-map-support@0.5.13:
  version "0.5.13"
  resolved "http://npm.htsc/source-map-support/download/source-map-support-0.5.13.tgz#31b24a9c2e73c2de85066c0feb7d44767ed52932"
  integrity sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-support@0.5.21, source-map-support@~0.5.20:
  version "0.5.21"
  resolved "http://npm.htsc/source-map-support/download/source-map-support-0.5.21.tgz#04fe7c7f9e1ed2d662233c28cb2b35b9f63f6e4f"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@0.7.3:
  version "0.7.3"
  resolved "http://npm.htsc/source-map/download/source-map-0.7.3.tgz#5302f8169031735226544092e64981f751750383"
  integrity sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=

source-map@0.7.4:
  version "0.7.4"
  resolved "http://registry.npm.htsc/source-map/-/source-map-0.7.4.tgz#a9bbe705c9d8846f4e08ff6765acf0f1b0898656"
  integrity sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==

source-map@^0.6.0, source-map@^0.6.1:
  version "0.6.1"
  resolved "http://npm.htsc/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

sourcemap-codec@^1.4.8:
  version "1.4.8"
  resolved "http://npm.htsc/sourcemap-codec/download/sourcemap-codec-1.4.8.tgz#ea804bd94857402e6992d05a38ef1ae35a9ab4c4"
  integrity sha1-6oBL2UhXQC5pktBaOO8a41qatMQ=

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "http://npm.htsc/spdx-correct/download/spdx-correct-3.1.1.tgz#dece81ac9c1e6713e5f7d1b6f17d468fa53d89a9"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "http://npm.htsc/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "http://npm.htsc/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.12"
  resolved "http://npm.htsc/spdx-license-ids/download/spdx-license-ids-3.0.12.tgz#69077835abe2710b65f03969898b6637b505a779"
  integrity sha1-aQd4NavicQtl8DlpiYtmN7UFp3k=

split2@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/split2/download/split2-4.1.0.tgz#101907a24370f85bb782f08adaabe4e281ecf809"
  integrity sha1-EBkHokNw+Fu3gvCK2qvk4oHs+Ak=

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://npm.htsc/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sqlstring@^2.3.2:
  version "2.3.3"
  resolved "http://npm.htsc/sqlstring/download/sqlstring-2.3.3.tgz#2ddc21f03bce2c387ed60680e739922c65751d0c"
  integrity sha1-Ldwh8DvOLDh+1gaA5zmSLGV1HQw=

sshpk@^1.7.0:
  version "1.17.0"
  resolved "http://npm.htsc/sshpk/download/sshpk-1.17.0.tgz#578082d92d4fe612b13007496e543fa0fbcbe4c5"
  integrity sha1-V4CC2S1P5hKxMAdJblQ/oPvL5MU=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

stack-trace@0.0.x:
  version "0.0.10"
  resolved "http://npm.htsc/stack-trace/download/stack-trace-0.0.10.tgz#547c70b347e8d32b4e108ea1a2a159e5fdde19c0"
  integrity sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA=

stack-utils@^2.0.3:
  version "2.0.5"
  resolved "http://npm.htsc/stack-utils/download/stack-utils-2.0.5.tgz#d25265fca995154659dbbfba3b49254778d2fdd5"
  integrity sha1-0lJl/KmVFUZZ27+6O0klR3jS/dU=
  dependencies:
    escape-string-regexp "^2.0.0"

standard-as-callback@^2.1.0:
  version "2.1.0"
  resolved "http://npm.htsc/standard-as-callback/download/standard-as-callback-2.1.0.tgz#8953fc05359868a77b5b9739a665c5977bb7df45"
  integrity sha1-iVP8BTWYaKd7W5c5pmXFl3u330U=

statuses@2.0.1:
  version "2.0.1"
  resolved "http://npm.htsc/statuses/download/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

streamifier@^0.1.1:
  version "0.1.1"
  resolved "http://npm.htsc/streamifier/download/streamifier-0.1.1.tgz#97e98d8fa4d105d62a2691d1dc07e820db8dfc4f"
  integrity sha1-l+mNj6TRBdYqJpHR3AfoINuN/E8=

streamsearch@^1.1.0:
  version "1.1.0"
  resolved "http://npm.htsc/streamsearch/download/streamsearch-1.1.0.tgz#404dd1e2247ca94af554e841a8ef0eaa238da764"
  integrity sha1-QE3R4iR8qUr1VOhBqO8OqiONp2Q=

string-length@^4.0.1:
  version "4.0.2"
  resolved "http://npm.htsc/string-length/download/string-length-4.0.2.tgz#a8a8dc7bd5c1a82b9b3c8b87e125f66871b6e57a"
  integrity sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "http://npm.htsc/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string.prototype.matchall@^4.0.7:
  version "4.0.7"
  resolved "http://npm.htsc/string.prototype.matchall/download/string.prototype.matchall-4.0.7.tgz#8e6ecb0d8a1fb1fda470d81acecb2dba057a481d"
  integrity sha1-jm7LDYofsf2kcNgazsstugV6SB0=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"
    get-intrinsic "^1.1.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.3"
    regexp.prototype.flags "^1.4.1"
    side-channel "^1.0.4"

string.prototype.trimend@^1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/string.prototype.trimend/download/string.prototype.trimend-1.0.5.tgz#914a65baaab25fbdd4ee291ca7dde57e869cb8d0"
  integrity sha1-kUpluqqyX73U7ikcp93lfoacuNA=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.19.5"

string.prototype.trimstart@^1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/string.prototype.trimstart/download/string.prototype.trimstart-1.0.5.tgz#5466d93ba58cfa2134839f81d7f42437e8c01fef"
  integrity sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.19.5"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://npm.htsc/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://npm.htsc/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/strip-bom/download/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/strip-bom/download/strip-bom-4.0.0.tgz#9c3505c1db45bcedca3d9cf7a16f5c5aa3901878"
  integrity sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/strip-final-newline/download/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-indent@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/strip-indent/download/strip-indent-3.0.0.tgz#c32e1cee940b6b3432c771bc2c54bcce73cd3001"
  integrity sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=
  dependencies:
    min-indent "^1.0.0"

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://npm.htsc/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

style-search@^0.1.0:
  version "0.1.0"
  resolved "http://npm.htsc/style-search/download/style-search-0.1.0.tgz#7958c793e47e32e07d2b5cafe5c0bf8e12e77902"
  integrity sha1-eVjHk+R+MuB9K1yv5cC/jhLneQI=

stylelint-config-css-modules@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/stylelint-config-css-modules/download/stylelint-config-css-modules-4.1.0.tgz#b507bc074ba5bfda9f40f0be79b540db249f0c78"
  integrity sha1-tQe8B0ulv9qfQPC+ebVA2ySfDHg=
  optionalDependencies:
    stylelint-scss "^4.2.0"

stylelint-config-prettier@^9.0.3:
  version "9.0.3"
  resolved "http://npm.htsc/stylelint-config-prettier/download/stylelint-config-prettier-9.0.3.tgz#0dccebeff359dcc393c9229184408b08964d561c"
  integrity sha1-Dczr7/NZ3MOTySKRhECLCJZNVhw=

stylelint-config-recommended@^8.0.0:
  version "8.0.0"
  resolved "http://npm.htsc/stylelint-config-recommended/download/stylelint-config-recommended-8.0.0.tgz#7736be9984246177f017c39ec7b1cd0f19ae9117"
  integrity sha1-dza+mYQkYXfwF8Oex7HNDxmukRc=

stylelint-config-standard@^26.0.0:
  version "26.0.0"
  resolved "http://npm.htsc/stylelint-config-standard/download/stylelint-config-standard-26.0.0.tgz#4701b8d582d34120eec7d260ba779e4c2d953635"
  integrity sha1-RwG41YLTQSDux9JguneeTC2VNjU=
  dependencies:
    stylelint-config-recommended "^8.0.0"

stylelint-declaration-block-no-ignored-properties@^2.5.0:
  version "2.5.0"
  resolved "http://npm.htsc/stylelint-declaration-block-no-ignored-properties/download/stylelint-declaration-block-no-ignored-properties-2.5.0.tgz#7cfe61c118ef5aa89f2bfdc2a78aa34bd2dacb87"
  integrity sha1-fP5hwRjvWqifK/3Cp4qjS9Lay4c=

stylelint-prettier@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/stylelint-prettier/download/stylelint-prettier-2.0.0.tgz#ead781aea522379f2ffa2d136bafdfc451d699a5"
  integrity sha1-6teBrqUiN58v+i0Ta6/fxFHWmaU=
  dependencies:
    prettier-linter-helpers "^1.0.0"

stylelint-scss@^4.2.0:
  version "4.3.0"
  resolved "http://npm.htsc/stylelint-scss/download/stylelint-scss-4.3.0.tgz#638800faf823db11fff60d537c81051fe74c90fa"
  integrity sha1-Y4gA+vgj2xH/9g1TfIEFH+dMkPo=
  dependencies:
    lodash "^4.17.21"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-selector-parser "^6.0.6"
    postcss-value-parser "^4.1.0"

stylelint@^14.15.0:
  version "14.15.0"
  resolved "http://npm.htsc/stylelint/download/stylelint-14.15.0.tgz#4df55078e734869f81f6b85bbec2d56a4b478ece"
  integrity sha1-TfVQeOc0hp+B9rhbvsLVaktHjs4=
  dependencies:
    "@csstools/selector-specificity" "^2.0.2"
    balanced-match "^2.0.0"
    colord "^2.9.3"
    cosmiconfig "^7.1.0"
    css-functions-list "^3.1.0"
    debug "^4.3.4"
    fast-glob "^3.2.12"
    fastest-levenshtein "^1.0.16"
    file-entry-cache "^6.0.1"
    global-modules "^2.0.0"
    globby "^11.1.0"
    globjoin "^0.1.4"
    html-tags "^3.2.0"
    ignore "^5.2.0"
    import-lazy "^4.0.0"
    imurmurhash "^0.1.4"
    is-plain-object "^5.0.0"
    known-css-properties "^0.26.0"
    mathml-tag-names "^2.1.3"
    meow "^9.0.0"
    micromatch "^4.0.5"
    normalize-path "^3.0.0"
    picocolors "^1.0.0"
    postcss "^8.4.19"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-safe-parser "^6.0.0"
    postcss-selector-parser "^6.0.10"
    postcss-value-parser "^4.2.0"
    resolve-from "^5.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    style-search "^0.1.0"
    supports-hyperlinks "^2.3.0"
    svg-tags "^1.0.0"
    table "^6.8.1"
    v8-compile-cache "^2.3.0"
    write-file-atomic "^4.0.2"

stylelint@^14.9.1:
  version "14.14.0"
  resolved "http://npm.htsc/stylelint/download/stylelint-14.14.0.tgz#1acb52497c9a921f23f9c4014d4e0ee6eba768d0"
  integrity sha1-GstSSXyakh8j+cQBTU4O5uunaNA=
  dependencies:
    "@csstools/selector-specificity" "^2.0.2"
    balanced-match "^2.0.0"
    colord "^2.9.3"
    cosmiconfig "^7.0.1"
    css-functions-list "^3.1.0"
    debug "^4.3.4"
    fast-glob "^3.2.12"
    fastest-levenshtein "^1.0.16"
    file-entry-cache "^6.0.1"
    global-modules "^2.0.0"
    globby "^11.1.0"
    globjoin "^0.1.4"
    html-tags "^3.2.0"
    ignore "^5.2.0"
    import-lazy "^4.0.0"
    imurmurhash "^0.1.4"
    is-plain-object "^5.0.0"
    known-css-properties "^0.25.0"
    mathml-tag-names "^2.1.3"
    meow "^9.0.0"
    micromatch "^4.0.5"
    normalize-path "^3.0.0"
    picocolors "^1.0.0"
    postcss "^8.4.17"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-safe-parser "^6.0.0"
    postcss-selector-parser "^6.0.10"
    postcss-value-parser "^4.2.0"
    resolve-from "^5.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    style-search "^0.1.0"
    supports-hyperlinks "^2.3.0"
    svg-tags "^1.0.0"
    table "^6.8.0"
    v8-compile-cache "^2.3.0"
    write-file-atomic "^4.0.2"

subscriptions-transport-ws@0.11.0:
  version "0.11.0"
  resolved "http://npm.htsc/subscriptions-transport-ws/download/subscriptions-transport-ws-0.11.0.tgz#baf88f050cba51d52afe781de5e81b3c31f89883"
  integrity sha1-uviPBQy6UdUq/ngd5egbPDH4mIM=
  dependencies:
    backo2 "^1.0.2"
    eventemitter3 "^3.1.0"
    iterall "^1.2.1"
    symbol-observable "^1.0.4"
    ws "^5.2.0 || ^6.0.0 || ^7.0.0"

superagent@^8.0.0:
  version "8.0.3"
  resolved "http://npm.htsc/superagent/download/superagent-8.0.3.tgz#15c8ec5611a1f01386994cfeeda5aa138bcb7b17"
  integrity sha1-FcjsVhGh8BOGmUz+7aWqE4vLexc=
  dependencies:
    component-emitter "^1.3.0"
    cookiejar "^2.1.3"
    debug "^4.3.4"
    fast-safe-stringify "^2.1.1"
    form-data "^4.0.0"
    formidable "^2.0.1"
    methods "^1.1.2"
    mime "2.6.0"
    qs "^6.11.0"
    semver "^7.3.8"

supertest@6.2.4:
  version "6.2.4"
  resolved "http://npm.htsc/supertest/download/supertest-6.2.4.tgz#3dcebe42f7fd6f28dd7ac74c6cba881f7101b2f0"
  integrity sha1-Pc6+Qvf9byjdesdMbLqIH3EBsvA=
  dependencies:
    methods "^1.1.2"
    superagent "^8.0.0"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://npm.htsc/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://npm.htsc/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "http://npm.htsc/supports-color/download/supports-color-8.1.1.tgz#cd6fc17e28500cff56c1b86c0a7fd4a54a73005c"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^2.0.0, supports-hyperlinks@^2.3.0:
  version "2.3.0"
  resolved "http://npm.htsc/supports-hyperlinks/download/supports-hyperlinks-2.3.0.tgz#3943544347c1ff90b15effb03fc14ae45ec10624"
  integrity sha1-OUNUQ0fB/5CxXv+wP8FK5F7BBiQ=
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/svg-tags/download/svg-tags-1.0.0.tgz#58f71cee3bd519b59d4b2a843b6c7de64ac04764"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

swagger-ui-dist@4.12.0:
  version "4.12.0"
  resolved "http://npm.htsc/swagger-ui-dist/download/swagger-ui-dist-4.12.0.tgz#986d90f05e81fb9db3ca40372278a5d8ce71db3a"
  integrity sha1-mG2Q8F6B+52zykA3Inil2M5x2zo=

symbol-observable@4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/symbol-observable/download/symbol-observable-4.0.0.tgz#5b425f192279e87f2f9b937ac8540d1984b39205"
  integrity sha1-W0JfGSJ56H8vm5N6yFQNGYSzkgU=

symbol-observable@^1.0.4:
  version "1.2.0"
  resolved "http://npm.htsc/symbol-observable/download/symbol-observable-1.2.0.tgz#c22688aed4eab3cdc2dfeacbb561660560a00804"
  integrity sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=

table@^6.0.9, table@^6.8.0:
  version "6.8.0"
  resolved "http://npm.htsc/table/download/table-6.8.0.tgz#87e28f14fa4321c3377ba286f07b79b281a3b3ca"
  integrity sha1-h+KPFPpDIcM3e6KG8Ht5soGjs8o=
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

table@^6.8.1:
  version "6.8.1"
  resolved "http://npm.htsc/table/download/table-6.8.1.tgz#ea2b71359fe03b017a5fbc296204471158080bdf"
  integrity sha1-6itxNZ/gOwF6X7wpYgRHEVgIC98=
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

tapable@^0.1.8:
  version "0.1.10"
  resolved "http://npm.htsc/tapable/download/tapable-0.1.10.tgz#29c35707c2b70e50d07482b5d202e8ed446dafd4"
  integrity sha1-KcNXB8K3DlDQdIK10gLo7URtr9Q=

tapable@^2.1.1, tapable@^2.2.0, tapable@^2.2.1:
  version "2.2.1"
  resolved "http://npm.htsc/tapable/download/tapable-2.2.1.tgz#1967a73ef4060a82f12ab96af86d52fdb76eeca0"
  integrity sha1-GWenPvQGCoLxKrlq+G1S/bdu7KA=

tar-stream@^1.5.2:
  version "1.6.2"
  resolved "http://npm.htsc/tar-stream/download/tar-stream-1.6.2.tgz#8ea55dab37972253d9a9af90fdcd559ae435c555"
  integrity sha1-jqVdqzeXIlPZqa+Q/c1VmuQ1xVU=
  dependencies:
    bl "^1.0.0"
    buffer-alloc "^1.2.0"
    end-of-stream "^1.0.0"
    fs-constants "^1.0.0"
    readable-stream "^2.3.0"
    to-buffer "^1.1.1"
    xtend "^4.0.0"

terminal-link@^2.0.0:
  version "2.1.1"
  resolved "http://npm.htsc/terminal-link/download/terminal-link-2.1.1.tgz#14a64a27ab3c0df933ea546fba55f2d078edc994"
  integrity sha1-FKZKJ6s8Dfkz6lRvulXy0HjtyZQ=
  dependencies:
    ansi-escapes "^4.2.1"
    supports-hyperlinks "^2.0.0"

terser-webpack-plugin@^5.1.3:
  version "5.3.6"
  resolved "http://npm.htsc/terser-webpack-plugin/download/terser-webpack-plugin-5.3.6.tgz#5590aec31aa3c6f771ce1b1acca60639eab3195c"
  integrity sha1-VZCuwxqjxvdxzhsazKYGOeqzGVw=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.14"
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.0"
    terser "^5.14.1"

terser@^5.14.1:
  version "5.15.1"
  resolved "http://npm.htsc/terser/download/terser-5.15.1.tgz#8561af6e0fd6d839669c73b92bdd5777d870ed6c"
  integrity sha1-hWGvbg/W2DlmnHO5K91Xd9hw7Ww=
  dependencies:
    "@jridgewell/source-map" "^0.3.2"
    acorn "^8.5.0"
    commander "^2.20.0"
    source-map-support "~0.5.20"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "http://npm.htsc/test-exclude/download/test-exclude-6.0.0.tgz#04a8698661d805ea6fa293b6cb9e63ac044ef15e"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-hex@1.0.x:
  version "1.0.0"
  resolved "http://npm.htsc/text-hex/download/text-hex-1.0.0.tgz#69dc9c1b17446ee79a92bf5b884bb4b9127506f5"
  integrity sha1-adycGxdEbueakr9biEu0uRJ1BvU=

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://npm.htsc/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://npm.htsc/thenify-all/download/thenify-all-1.6.0.tgz#1a1918d402d8fc3f98fbf234db0bcc8cc10e9726"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "http://npm.htsc/thenify/download/thenify-3.3.1.tgz#8932e686a4066038a016dd9e2ca46add9838a95f"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

through@^2.3.6:
  version "2.3.8"
  resolved "http://npm.htsc/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

tmp@^0.0.33:
  version "0.0.33"
  resolved "http://npm.htsc/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.5:
  version "1.0.5"
  resolved "http://npm.htsc/tmpl/download/tmpl-1.0.5.tgz#8683e0b902bb9c20c4f726e3c0b69f36518c07cc"
  integrity sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=

to-buffer@^1.1.1:
  version "1.1.1"
  resolved "http://npm.htsc/to-buffer/download/to-buffer-1.1.1.tgz#493bd48f62d7c43fcded313a03dcadb2e1213a80"
  integrity sha1-STvUj2LXxD/N7TE6A9ytsuEhOoA=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://npm.htsc/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/toidentifier/download/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "http://npm.htsc/tough-cookie/download/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "http://npm.htsc/tr46/download/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

tree-kill@1.2.2:
  version "1.2.2"
  resolved "http://npm.htsc/tree-kill/download/tree-kill-1.2.2.tgz#4ca09a9092c88b73a7cdc5e8a01b507b0790a0cc"
  integrity sha1-TKCakJLIi3OnzcXooBtQeweQoMw=

trim-newlines@^3.0.0:
  version "3.0.1"
  resolved "http://npm.htsc/trim-newlines/download/trim-newlines-3.0.1.tgz#260a5d962d8b752425b32f3a7db0dcacd176c144"
  integrity sha1-Jgpdli2LdSQlsy86fbDcrNF2wUQ=

triple-beam@^1.3.0:
  version "1.3.0"
  resolved "http://npm.htsc/triple-beam/download/triple-beam-1.3.0.tgz#a595214c7298db8339eeeee083e4d10bd8cb8dd9"
  integrity sha1-pZUhTHKY24M57u7gg+TRC9jLjdk=

ts-jest@28.0.7:
  version "28.0.7"
  resolved "http://npm.htsc/ts-jest/download/ts-jest-28.0.7.tgz#e18757a9e44693da9980a79127e5df5a98b37ac6"
  integrity sha1-4YdXqeRGk9qZgKeRJ+XfWpizesY=
  dependencies:
    bs-logger "0.x"
    fast-json-stable-stringify "2.x"
    jest-util "^28.0.0"
    json5 "^2.2.1"
    lodash.memoize "4.x"
    make-error "1.x"
    semver "7.x"
    yargs-parser "^21.0.1"

ts-loader@9.3.1:
  version "9.3.1"
  resolved "http://npm.htsc/ts-loader/download/ts-loader-9.3.1.tgz#fe25cca56e3e71c1087fe48dc67f4df8c59b22d4"
  integrity sha1-/iXMpW4+ccEIf+SNxn9N+MWbItQ=
  dependencies:
    chalk "^4.1.0"
    enhanced-resolve "^5.0.0"
    micromatch "^4.0.0"
    semver "^7.3.4"

ts-morph@^15.1.0:
  version "15.1.0"
  resolved "http://npm.htsc/ts-morph/download/ts-morph-15.1.0.tgz#53deea5296d967ff6eba8f15f99d378aa7074a4e"
  integrity sha1-U97qUpbZZ/9uuo8V+Z03iqcHSk4=
  dependencies:
    "@ts-morph/common" "~0.16.0"
    code-block-writer "^11.0.0"

ts-node@10.9.1:
  version "10.9.1"
  resolved "http://npm.htsc/ts-node/download/ts-node-10.9.1.tgz#e73de9102958af9e1f0b168a6ff320e25adcff4b"
  integrity sha1-5z3pEClYr54fCxaKb/Mg4lrc/0s=
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tsconfig-paths-webpack-plugin@3.5.2:
  version "3.5.2"
  resolved "http://npm.htsc/tsconfig-paths-webpack-plugin/download/tsconfig-paths-webpack-plugin-3.5.2.tgz#01aafff59130c04a8c4ebc96a3045c43c376449a"
  integrity sha1-Aar/9ZEwwEqMTryWowRcQ8N2RJo=
  dependencies:
    chalk "^4.1.0"
    enhanced-resolve "^5.7.0"
    tsconfig-paths "^3.9.0"

tsconfig-paths@3.14.1, tsconfig-paths@^3.14.1, tsconfig-paths@^3.9.0:
  version "3.14.1"
  resolved "http://npm.htsc/tsconfig-paths/download/tsconfig-paths-3.14.1.tgz#ba0734599e8ea36c862798e920bcf163277b137a"
  integrity sha1-ugc0WZ6Oo2yGJ5jpILzxYyd7E3o=
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.1"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tsconfig-paths@4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/tsconfig-paths/download/tsconfig-paths-4.0.0.tgz#1082f5d99fd127b72397eef4809e4dd06d229b64"
  integrity sha1-EIL12Z/RJ7cjl+70gJ5N0G0im2Q=
  dependencies:
    json5 "^2.2.1"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@2.4.0, tslib@^2.1.0, tslib@^2.3.1:
  version "2.4.0"
  resolved "http://npm.htsc/tslib/download/tslib-2.4.0.tgz#7cecaa7f073ce680a05847aa77be941098f36dc3"
  integrity sha1-fOyqfwc85oCgWEeqd76UEJjzbcM=

tslib@2.4.1:
  version "2.4.1"
  resolved "http://npm.htsc/tslib/download/tslib-2.4.1.tgz#0d0bfbaac2880b91e22df0768e55be9753a5b17e"
  integrity sha1-DQv7qsKIC5HiLfB2jlW+l1OlsX4=

tslib@2.5.0:
  version "2.5.0"
  resolved "http://npm.htsc/tslib/download/tslib-2.5.0.tgz#42bfed86f5787aeb41d031866c8f402429e0fddf"
  integrity sha1-Qr/thvV4eutB0DGGbI9AJCng/d8=

tslib@2.5.3, tslib@^2.4.0:
  version "2.5.3"
  resolved "http://npm.htsc/tslib/download/tslib-2.5.3.tgz#24944ba2d990940e6e982c4bea147aba80209913"
  integrity sha1-JJRLotmQlA5umCxL6hR6uoAgmRM=

tslib@^1.8.1, tslib@^1.9.0:
  version "1.14.1"
  resolved "http://npm.htsc/tslib/download/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tsutils@^3.21.0:
  version "3.21.0"
  resolved "http://npm.htsc/tsutils/download/tsutils-3.21.0.tgz#b48717d394cea6c1e096983eed58e9d61715b623"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://npm.htsc/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "http://npm.htsc/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://npm.htsc/type-check/download/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-detect@4.0.8:
  version "4.0.8"
  resolved "http://npm.htsc/type-detect/download/type-detect-4.0.8.tgz#7646fb5f18871cfbb7749e69bd39a6388eb7450c"
  integrity sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=

type-fest@^0.18.0:
  version "0.18.1"
  resolved "http://npm.htsc/type-fest/download/type-fest-0.18.1.tgz#db4bc151a4a2cf4eebf9add5db75508db6cc841f"
  integrity sha1-20vBUaSiz07r+a3V23VQjbbMhB8=

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://npm.htsc/type-fest/download/type-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "http://npm.htsc/type-fest/download/type-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "http://npm.htsc/type-fest/download/type-fest-0.6.0.tgz#8d2a2370d3df886eb5c90ada1c5bf6188acf838b"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "http://npm.htsc/type-fest/download/type-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@^1.6.4, type-is@~1.6.18:
  version "1.6.18"
  resolved "http://npm.htsc/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "http://npm.htsc/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

typeorm-naming-strategies@^4.1.0:
  version "4.1.0"
  resolved "http://npm.htsc/typeorm-naming-strategies/download/typeorm-naming-strategies-4.1.0.tgz#1ec6eb296c8d7b69bb06764d5b9083ff80e814a9"
  integrity sha1-HsbrKWyNe2m7BnZNW5CD/4DoFKk=

typeorm@0.3.10:
  version "0.3.10"
  resolved "http://npm.htsc/typeorm/download/typeorm-0.3.10.tgz#aa2857fd4b078c912ca693b7eee01b6535704458"
  integrity sha1-qihX/UsHjJEsppO37uAbZTVwRFg=
  dependencies:
    "@sqltools/formatter" "^1.2.2"
    app-root-path "^3.0.0"
    buffer "^6.0.3"
    chalk "^4.1.0"
    cli-highlight "^2.1.11"
    date-fns "^2.28.0"
    debug "^4.3.3"
    dotenv "^16.0.0"
    glob "^7.2.0"
    js-yaml "^4.1.0"
    mkdirp "^1.0.4"
    reflect-metadata "^0.1.13"
    sha.js "^2.4.11"
    tslib "^2.3.1"
    uuid "^8.3.2"
    xml2js "^0.4.23"
    yargs "^17.3.1"

typescript@*:
  version "4.8.4"
  resolved "http://npm.htsc/typescript/download/typescript-4.8.4.tgz#c464abca159669597be5f96b8943500b238e60e6"
  integrity sha1-xGSryhWWaVl75flriUNQCyOOYOY=

typescript@4.7.4:
  version "4.7.4"
  resolved "http://npm.htsc/typescript/download/typescript-4.7.4.tgz#1a88596d1cf47d59507a1bcdfb5b9dfe4d488235"
  integrity sha1-GohZbRz0fVlQehvN+1ud/k1IgjU=

uid@2.0.2:
  version "2.0.2"
  resolved "http://npm.htsc/uid/download/uid-2.0.2.tgz#4b5782abf0f2feeefc00fa88006b2b3b7af3e3b9"
  integrity sha1-S1eCq/Dy/u78APqIAGsrO3rz47k=
  dependencies:
    "@lukeed/csprng" "^1.0.0"

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/unbox-primitive/download/unbox-primitive-1.0.2.tgz#29032021057d5e6cdbd08c5129c226dff8ed6f9e"
  integrity sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54=
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.0.tgz#301acdc525631670d39f6146e0e77ff6bbdebddc"
  integrity sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz#54fd16e0ecb167cf04cf1f756bdcc92eba7976c3"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.0.0.tgz#1a01aa57247c14c568b89775a54938788189a714"
  integrity sha1-GgGqVyR8FMVouJd1pUk4eIGJpxQ=

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "http://npm.htsc/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz#43d41e3be698bd493ef911077c9b131f827e8ccd"
  integrity sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=

unist-util-stringify-position@^2.0.0:
  version "2.0.3"
  resolved "http://npm.htsc/unist-util-stringify-position/download/unist-util-stringify-position-2.0.3.tgz#cce3bfa1cdf85ba7375d1d5b17bdc4cada9bd9da"
  integrity sha1-zOO/oc34W6c3XR1bF73Eytqb2do=
  dependencies:
    "@types/unist" "^2.0.2"

universalify@^0.1.0:
  version "0.1.2"
  resolved "http://npm.htsc/universalify/download/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

universalify@^2.0.0:
  version "2.0.0"
  resolved "http://npm.htsc/universalify/download/universalify-2.0.0.tgz#75a4984efedc4b08975c5aeb73f530d02df25717"
  integrity sha1-daSYTv7cSwiXXFrrc/Uw0C3yVxc=

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "http://npm.htsc/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

update-browserslist-db@^1.0.9:
  version "1.0.9"
  resolved "http://npm.htsc/update-browserslist-db/download/update-browserslist-db-1.0.9.tgz#2924d3927367a38d5c555413a7ce138fc95fcb18"
  integrity sha1-KSTTknNno41cVVQTp84Tj8lfyxg=
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://npm.htsc/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://npm.htsc/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util@^0.12.4:
  version "0.12.5"
  resolved "http://npm.htsc/util/download/util-0.12.5.tgz#5f17a6059b73db61a875668781a1c2b136bd6fbc"
  integrity sha1-XxemBZtz22GodWaHgaHCsTa9b7w=
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

utils-merge@1.0.1, utils-merge@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/utils-merge/download/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@8.3.2, uuid@^8.3.2:
  version "8.3.2"
  resolved "http://npm.htsc/uuid/download/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

uuid@9.0.0, uuid@^9.0.0:
  version "9.0.0"
  resolved "http://npm.htsc/uuid/download/uuid-9.0.0.tgz#592f550650024a38ceb0c562f2f6aa435761efb5"
  integrity sha1-WS9VBlACSjjOsMVi8vaqQ1dh77U=

uuid@^11.0.2:
  version "11.0.2"
  resolved "http://registry.npm.htsc/uuid/-/uuid-11.0.2.tgz#a8d68ba7347d051e7ea716cc8dcbbab634d66875"
  integrity sha512-14FfcOJmqdjbBPdDjFQyk/SdT4NySW4eM0zcG+HqbHP5jzuH56xO3J1DGhgs/cEMCfwYi3HQI1gnTO62iaG+tQ==

uuid@^3.3.2:
  version "3.4.0"
  resolved "http://npm.htsc/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "http://npm.htsc/v8-compile-cache-lib/download/v8-compile-cache-lib-3.0.1.tgz#6336e8d71965cb3d35a1bbb7868445a7c05264bf"
  integrity sha1-Yzbo1xllyz01obu3hoRFp8BSZL8=

v8-compile-cache@^2.0.3, v8-compile-cache@^2.3.0:
  version "2.3.0"
  resolved "http://npm.htsc/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz#2de19618c66dc247dcfb6f99338035d8245a2cee"
  integrity sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4=

v8-to-istanbul@^9.0.1:
  version "9.0.1"
  resolved "http://npm.htsc/v8-to-istanbul/download/v8-to-istanbul-9.0.1.tgz#b6f994b0b5d4ef255e17a0d17dc444a9f5132fa4"
  integrity sha1-tvmUsLXU7yVeF6DRfcREqfUTL6Q=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.12"
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^1.6.0"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://npm.htsc/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

validator@^13.7.0:
  version "13.7.0"
  resolved "http://npm.htsc/validator/download/validator-13.7.0.tgz#4f9658ba13ba8f3d82ee881d3516489ea85c0857"
  integrity sha1-T5ZYuhO6jz2C7ogdNRZInqhcCFc=

value-or-promise@1.0.12, value-or-promise@^1.0.12:
  version "1.0.12"
  resolved "http://npm.htsc/value-or-promise/download/value-or-promise-1.0.12.tgz#0e5abfeec70148c78460a849f6b003ea7986f15c"
  integrity sha1-Dlq/7scBSMeEYKhJ9rAD6nmG8Vw=

vary@^1, vary@~1.1.2:
  version "1.1.2"
  resolved "http://npm.htsc/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

verror@1.10.0:
  version "1.10.0"
  resolved "http://npm.htsc/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vue-eslint-parser@^8.0.1:
  version "8.3.0"
  resolved "http://npm.htsc/vue-eslint-parser/download/vue-eslint-parser-8.3.0.tgz#5d31129a1b3dd89c0069ca0a1c88f970c360bd0d"
  integrity sha1-XTESmhs92JwAacoKHIj5cMNgvQ0=
  dependencies:
    debug "^4.3.2"
    eslint-scope "^7.0.0"
    eslint-visitor-keys "^3.1.0"
    espree "^9.0.0"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^7.3.5"

walker@^1.0.8:
  version "1.0.8"
  resolved "http://npm.htsc/walker/download/walker-1.0.8.tgz#bd498db477afe573dc04185f011d3ab8a8d7653f"
  integrity sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=
  dependencies:
    makeerror "1.0.12"

watchpack@^2.3.1:
  version "2.4.0"
  resolved "http://npm.htsc/watchpack/download/watchpack-2.4.0.tgz#fa33032374962c78113f93c7f2fb4c54c9862a5d"
  integrity sha1-+jMDI3SWLHgRP5PH8vtMVMmGKl0=
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://npm.htsc/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "http://npm.htsc/webidl-conversions/download/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webpack-node-externals@3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/webpack-node-externals/download/webpack-node-externals-3.0.0.tgz#1a3407c158d547a9feb4229a9e3385b7b60c9917"
  integrity sha1-GjQHwVjVR6n+tCKanjOFt7YMmRc=

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "http://npm.htsc/webpack-sources/download/webpack-sources-3.2.3.tgz#2d4daab8451fd4b240cc27055ff6a0c2ccea0cde"
  integrity sha1-LU2quEUf1LJAzCcFX/agwszqDN4=

webpack@5.73.0:
  version "5.73.0"
  resolved "http://npm.htsc/webpack/download/webpack-5.73.0.tgz#bbd17738f8a53ee5760ea2f59dce7f3431d35d38"
  integrity sha1-u9F3OPilPuV2DqL1nc5/NDHTXTg=
  dependencies:
    "@types/eslint-scope" "^3.7.3"
    "@types/estree" "^0.0.51"
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/wasm-edit" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    acorn "^8.4.1"
    acorn-import-assertions "^1.7.6"
    browserslist "^4.14.5"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.9.3"
    es-module-lexer "^0.9.0"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.9"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.1.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.1.3"
    watchpack "^2.3.1"
    webpack-sources "^3.2.3"

whatwg-mimetype@^3.0.0:
  version "3.0.0"
  resolved "http://npm.htsc/whatwg-mimetype/download/whatwg-mimetype-3.0.0.tgz#5fa1a7623867ff1af6ca3dc72ad6b8a4208beba7"
  integrity sha1-X6GnYjhn/xr2yj3HKta4pCCL66c=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "http://npm.htsc/whatwg-url/download/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "http://npm.htsc/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz#13757bc89b209b049fe5d86430e21cf40a89a8e6"
  integrity sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-typed-array@^1.1.2:
  version "1.1.9"
  resolved "http://npm.htsc/which-typed-array/download/which-typed-array-1.1.9.tgz#307cf898025848cf995e795e8423c7f337efbde6"
  integrity sha1-MHz4mAJYSM+ZXnlehCPH8zfvveY=
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"
    is-typed-array "^1.1.10"

which@^1.3.1:
  version "1.3.1"
  resolved "http://npm.htsc/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "http://npm.htsc/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

windows-release@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/windows-release/download/windows-release-4.0.0.tgz#4725ec70217d1bf6e02c7772413b29cdde9ec377"
  integrity sha1-RyXscCF9G/bgLHdyQTspzd6ew3c=
  dependencies:
    execa "^4.0.2"

winston-transport@^4.4.0, winston-transport@^4.5.0:
  version "4.5.0"
  resolved "http://npm.htsc/winston-transport/download/winston-transport-4.5.0.tgz#6e7b0dd04d393171ed5e4e4905db265f7ab384fa"
  integrity sha1-bnsN0E05MXHtXk5JBdsmX3qzhPo=
  dependencies:
    logform "^2.3.2"
    readable-stream "^3.6.0"
    triple-beam "^1.3.0"

winston@^3.8.2:
  version "3.8.2"
  resolved "http://npm.htsc/winston/download/winston-3.8.2.tgz#56e16b34022eb4cff2638196d9646d7430fdad50"
  integrity sha1-VuFrNAIutM/yY4GW2WRtdDD9rVA=
  dependencies:
    "@colors/colors" "1.5.0"
    "@dabh/diagnostics" "^2.0.2"
    async "^3.2.3"
    is-stream "^2.0.0"
    logform "^2.4.0"
    one-time "^1.0.0"
    readable-stream "^3.4.0"
    safe-stable-stringify "^2.3.1"
    stack-trace "0.0.x"
    triple-beam "^1.3.0"
    winston-transport "^4.5.0"

word-wrap@^1.2.3:
  version "1.2.3"
  resolved "http://npm.htsc/word-wrap/download/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "http://registry.npm.htsc/wrap-ansi/-/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "http://npm.htsc/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://npm.htsc/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^4.0.1, write-file-atomic@^4.0.2:
  version "4.0.2"
  resolved "http://npm.htsc/write-file-atomic/download/write-file-atomic-4.0.2.tgz#a9df01ae5b77858a027fd2e80768ee433555fcfd"
  integrity sha1-qd8Brlt3hYoCf9LoB2juQzVV/P0=
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^3.0.7"

ws@8.13.0:
  version "8.13.0"
  resolved "http://npm.htsc/ws/download/ws-8.13.0.tgz#9a9fb92f93cf41512a0735c8f4dd09b8a1211cd0"
  integrity sha1-mp+5L5PPQVEqBzXI9N0JuKEhHNA=

"ws@^5.2.0 || ^6.0.0 || ^7.0.0":
  version "7.5.9"
  resolved "http://npm.htsc/ws/download/ws-7.5.9.tgz#54fa7db29f4c7cec68b1ddd3a89de099942bb591"
  integrity sha1-VPp9sp9MfOxosd3TqJ3gmZQrtZE=

xml2js@^0.4.23:
  version "0.4.23"
  resolved "http://npm.htsc/xml2js/download/xml2js-0.4.23.tgz#a0c69516752421eb2ac758ee4d4ccf58843eac66"
  integrity sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY=
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "http://npm.htsc/xmlbuilder/download/xmlbuilder-11.0.1.tgz#be9bae1c8a046e76b31127726347d0ad7002beb3"
  integrity sha1-vpuuHIoEbnazESdyY0fQrXACvrM=

xss@^1.0.8:
  version "1.0.14"
  resolved "http://npm.htsc/xss/download/xss-1.0.14.tgz#4f3efbde75ad0d82e9921cc3c95e6590dd336694"
  integrity sha1-Tz773nWtDYLpkhzDyV5lkN0zZpQ=
  dependencies:
    commander "^2.20.3"
    cssfilter "0.0.10"

xtend@^4.0.0:
  version "4.0.2"
  resolved "http://npm.htsc/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^5.0.5:
  version "5.0.8"
  resolved "http://npm.htsc/y18n/download/y18n-5.0.8.tgz#7f4934d0f7ca8c56f95314939ddcd2dd91ce1d55"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^2.1.2:
  version "2.1.2"
  resolved "http://npm.htsc/yallist/download/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^4.0.0:
  version "4.0.0"
  resolved "http://npm.htsc/yallist/download/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.10.0:
  version "1.10.2"
  resolved "http://npm.htsc/yaml/download/yaml-1.10.2.tgz#2301c5ffbf12b467de8da2333a459e29e7920e4b"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yargs-parser@21.0.1:
  version "21.0.1"
  resolved "http://npm.htsc/yargs-parser/download/yargs-parser-21.0.1.tgz#0267f286c877a4f0f728fceb6f8a3e4cb95c6e35"
  integrity sha1-Amfyhsh3pPD3KPzrb4o+TLlcbjU=

yargs-parser@21.1.1, yargs-parser@^21.0.0, yargs-parser@^21.0.1:
  version "21.1.1"
  resolved "http://npm.htsc/yargs-parser/download/yargs-parser-21.1.1.tgz#9096bceebf990d21bb31fa9516e0ede294a77d35"
  integrity sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=

yargs-parser@^20.2.2, yargs-parser@^20.2.3:
  version "20.2.9"
  resolved "http://npm.htsc/yargs-parser/download/yargs-parser-20.2.9.tgz#2eb7dc3b0289718fc295f362753845c41a0c94ee"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs@^16.0.0:
  version "16.2.0"
  resolved "http://npm.htsc/yargs/download/yargs-16.2.0.tgz#1c82bf0f6b6a66eafce7ef30e376f49a12477f66"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yargs@^17.3.1:
  version "17.6.0"
  resolved "http://npm.htsc/yargs/download/yargs-17.6.0.tgz#e134900fc1f218bc230192bdec06a0a5f973e46c"
  integrity sha1-4TSQD8HyGLwjAZK97Aagpflz5Gw=
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.0.0"

yauzl@^2.7.0:
  version "2.10.0"
  resolved "http://npm.htsc/yauzl/download/yauzl-2.10.0.tgz#c7eb17c93e112cb1086fa6d8e51fb0667b79a5f9"
  integrity sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.1.0"

yazl@^2.4.2:
  version "2.5.1"
  resolved "http://npm.htsc/yazl/download/yazl-2.5.1.tgz#a3d65d3dd659a5b0937850e8609f22fffa2b5c35"
  integrity sha1-o9ZdPdZZpbCTeFDoYJ8i//orXDU=
  dependencies:
    buffer-crc32 "~0.2.3"

yn@3.1.1:
  version "3.1.1"
  resolved "http://npm.htsc/yn/download/yn-3.1.1.tgz#1e87401a09d767c1d5eab26a6e4c185182d2eb50"
  integrity sha1-HodAGgnXZ8HV6rJqbkwYUYLS61A=

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "http://npm.htsc/yocto-queue/download/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=
