import * as HtTrace from '@ht/node-trace/src/core';
import { Request } from 'express';
import * as ip from 'ip';
import { v4 as uuid } from 'uuid';

interface TraceOpt {
  appName: string;
  env: 'prod' | 'dev';
  initial: boolean;
}

export class Tracer {
  private instance: HtTrace;

  private options: TraceOpt;

  constructor(options: TraceOpt) {
    this.options = options;
    this.instance = new HtTrace(options);
  }

  public sendMessage(req: Request, startTime: number, success: boolean) {
    const { appName, initial } = this.options;
    const host = req.hostname;
    const port = host.split(':')[1];
    const payload = {
      appName,
      initial,
      startTime,
      endTime: Date.now(),
      protocol: req.protocol,
      traceId: req.headers.traceid || uuid(),
      chainId: req.headers.chainid || '0',
      consumerHost: ip.address(),
      consumerPort: port,
      serviceName: req.url,
      methodName: req.method,
      tags: {},
      success,
    };
    this.instance.sendMessage(payload);
  }
}
