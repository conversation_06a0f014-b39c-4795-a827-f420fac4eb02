import { v4 as uuid } from 'uuid';
import { isNil } from 'lodash';
/**
 * 生成或更新 chainid
 * @param {string} chainid - 类似 '0' | '0.0' 的字符串
 * @param {string} replacement - 指定的替换字符串，例如 'xxxxxx'
 * @returns {string | number} - 更新后的 chainid，例如 '0' | '0.0' | '0.xxxxxx' | '0.0.xxxxxx'
 */
export function updateChainId(chainid, replacement = '') {
  try {
    if (isNil(chainid)) {
      return '0';
    }
    if (replacement) {
      // 如果 chainid 只有一个部分，直接返回 '0.xxxxxx'
      if (!chainid.includes('.')) {
        return `${chainid}.${replacement}`;
      }

      // 否则，将最后一位的 '.xxx' 替换为指定的 '.xxxxxx'
      const parts = chainid.split('.');
      parts[parts.length - 1] = replacement;
      return parts.join('.');
    }
    // 如果 chainid 只有一个部分，直接返回 '0.0'
    if (!chainid.includes('.')) {
      return `${chainid}.0`;
    }

    // 否则，在末尾添加 '.0'
    return `${chainid}.0`;
  } catch (error) {
    return chainid || '0';
  }
}

/**
 * 基于uuid生成traceid
 * @returns
 */
export function genTraceId() {
  return uuid();
}
