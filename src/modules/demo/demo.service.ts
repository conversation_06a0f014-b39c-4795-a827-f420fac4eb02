import { Injectable } from '@nestjs/common';
import * as compressing from 'compressing';
import * as fs from 'fs';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';
import {
  paginate,
  Pagination,
  paginateRaw,
  IPaginationOptions,
} from 'nestjs-typeorm-paginate';

import { QueryDemoeDTO } from './dtos/index.dto';
import { Demo } from './entities/index.entity';

@Injectable()
export class DemoService {
  constructor(
    @InjectRepository(Demo)
    private demoRepository: Repository<Demo>
  ) {}

  async getDemo(name: string): Promise<QueryDemoeDTO> {
    return {
      id: 1,
      name,
      createTime: new Date(),
      updateTime: new Date(),
    };
  }
}
