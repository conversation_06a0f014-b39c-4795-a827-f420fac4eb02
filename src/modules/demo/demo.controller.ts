import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam } from '@nestjs/swagger';
import { CtripApolloClientService } from 'nestjs-ctrip-apollo-client';

import { Res } from 'src/common/dto/res.dto';
import { ErrorCode } from 'src/error/error-code';

import { DemoService } from './demo.service';

@ApiTags('demo接口')
@Controller('/v1/demo')
export class DemoController {
  constructor(
    private readonly demoService: DemoService,
    private readonly ctripApolloClientService: CtripApolloClientService
  ) {}

  @Get(':name')
  @ApiOperation({
    summary: '获取demo详情',
  })
  @ApiParam({
    name: 'name',
    description: '名称',
    example: 'aaa',
  })
  async getDemo(@Param('name') name: string) {
    try {
      // apollo配置数据
      const testApolloConfig = await this.ctripApolloClientService.fetchConfig({
        key: 'test_key',
      });
      const res = await this.demoService.getDemo(name);
      return new Res().success({
        ...res,
        testApolloConfig,
      });
    } catch (e) {
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, '未找到相应记录');
    }
  }
}
