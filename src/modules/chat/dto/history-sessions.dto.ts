import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

/**
 * 获取历史会话列表请求 DTO
 */
export class GetHistorySessionsDto {
  @ApiProperty({
    description: '系统id，标识平台',
    example: 'aorta',
  })
  @IsString()
  appid: string;

  @ApiProperty({
    description: '当前系统登录用户id',
    example: 'user123',
  })
  @IsString()
  userid: string;

  @ApiProperty({
    description: 'hiagent接口涉及，鉴权字段',
    required: false,
    example: 'your-app-key',
  })
  @IsOptional()
  @IsString()
  appKey?: string;
}

/**
 * 会话信息 DTO
 */
export class SessionInfoDto {
  @ApiProperty({
    description: '会话id',
    example: 'conv_123456789',
  })
  conversationId: string;

  @ApiProperty({
    description: '该笔会话的第一个问题',
    example: '你好，请问如何使用这个系统？',
  })
  question: string;

  @ApiProperty({
    description: '会话创建时间（会话第一条消息时间）',
    example: '2024-01-15 10:30:00',
  })
  createTime: string;

  @ApiProperty({
    description: '会话更新时间（会话最后一条消息时间）',
    example: '2024-01-15 11:45:00',
  })
  updateTime: string;
}

/**
 * 获取历史会话列表响应 DTO
 */
export class GetHistorySessionsResponseDto {
  @ApiProperty({
    description: '状态码，0表示成功，非0表示失败',
    example: '0',
  })
  code: string;

  @ApiProperty({
    description: '失败时的错误消息',
    example: '操作成功',
  })
  msg: string;

  @ApiProperty({
    description: '响应数据',
    type: 'object',
    properties: {
      list: {
        type: 'array',
        items: { $ref: '#/components/schemas/SessionInfoDto' },
        description: '包含会话相关信息的数组',
      },
    },
  })
  resultData: {
    list: SessionInfoDto[];
  };
}
