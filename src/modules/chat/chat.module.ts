import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';

import { ChatService } from './chat.service';
import { ChatController } from './chat.controller';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000, // 30秒超时
      maxRedirects: 5,
      retries: 3, // 重试3次
      retryDelay: 1000, // 重试间隔1秒
    }),
  ],
  controllers: [ChatController],
  providers: [ChatService],
  exports: [ChatService],
})
export class ChatModule {}
