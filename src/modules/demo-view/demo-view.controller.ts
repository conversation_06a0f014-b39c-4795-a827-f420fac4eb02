import { <PERSON>, Get, Param, Query, Render } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam } from '@nestjs/swagger';
import { DemoService } from 'src/modules/demo/demo.service';
import { Res } from 'src/common/dto/res.dto';
import { ErrorCode } from 'src/error/error-code';

@ApiTags('view页面')
@Controller('/demo-view')
export class DemoViewController {
  constructor(private readonly demoService: DemoService) {}

  @Get([':name'])
  @Render('demo-view')
  @ApiOperation({
    summary: 'demo页面',
  })
  @ApiParam({
    name: 'name',
    description: '名称',
    example: 'aaa',
  })
  async root(@Param('name') name: string) {
    const prefix = `/web-assistant`;
    try {
      const res = await this.demoService.getDemo(name);
      return {
        prefix,
        ...res,
      };
    } catch (e) {
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, '未知错误');
    }
  }
}
