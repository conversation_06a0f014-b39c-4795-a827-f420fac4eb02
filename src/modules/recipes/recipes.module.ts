import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DateScalar } from 'src/common/scalars/date.scalar';
import { RecipesResolver } from './recipes.resolver';
import { RecipesService } from './recipes.service';
import { RecipeEntity } from './entities/recipe.entity';

/**
 * graphql测试接口
 *
 * - graphql
 * @link https://docs.nestjs.com/graphql/quick-start
 * - register RecipesResolver as a provider
 * @link https://docs.nestjs.com/graphql/resolvers#module
 * - register DateScalar as a provider
 * @link https://docs.nestjs.com/graphql/scalars#override-a-default-scalar
 */
@Module({
  imports: [TypeOrmModule.forFeature([RecipeEntity])],
  providers: [RecipesResolver, RecipesService, DateScalar],
})
export class RecipesModule {}
