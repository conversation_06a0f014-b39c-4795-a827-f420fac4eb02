import { NotFoundException } from '@nestjs/common';
import { Args, Mutation, Query, Resolver, Subscription } from '@nestjs/graphql';
import { PubSub } from 'graphql-subscriptions';
import { Res } from 'src/common/dto/res.dto';
import { ErrorCode } from 'src/error/error-code';
import { NewRecipeInput } from './dtos/new-recipe.input';
import { RecipesArgs } from './dtos/recipes.args';
import { RecipeEntity } from './entities/recipe.entity';
import { RecipesService } from './recipes.service';

const pubSub = new PubSub();

/**
 * 查询、变更、订阅
 */
@Resolver((of) => RecipeEntity)
export class RecipesResolver {
  constructor(private readonly recipesService: RecipesService) {}

  @Query((returns) => RecipeEntity, {
    name: 'recipe',
    description: '菜单详情',
  })
  async getRecipe(@Args('id') id: string): Promise<RecipeEntity> {
    const recipe = await this.recipesService.findOneById(id);
    if (!recipe) {
      throw new NotFoundException(id);
    }
    return recipe;
  }

  @Query((returns) => [RecipeEntity], { description: '菜单列表' })
  recipes(@Args() recipesArgs: RecipesArgs): Promise<RecipeEntity[]> {
    return this.recipesService.findAll(recipesArgs);
  }

  @Mutation((returns) => RecipeEntity, { description: '新增菜单' })
  async addRecipe(
    @Args('newRecipeData') newRecipeData: NewRecipeInput
  ): Promise<RecipeEntity> {
    const recipe = await this.recipesService.create(newRecipeData);
    pubSub.publish('recipeAdded', { recipeAdded: recipe });
    return recipe;
  }

  @Mutation((returns) => Boolean, { description: '删除菜单' })
  async removeRecipe(@Args('id') id: string) {
    return this.recipesService.remove(id);
  }

  @Subscription((returns) => RecipeEntity, { description: '订阅新增' })
  recipeAdded() {
    return pubSub.asyncIterator('recipeAdded');
  }
}
