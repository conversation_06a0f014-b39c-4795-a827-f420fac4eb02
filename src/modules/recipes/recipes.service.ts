import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NewRecipeInput } from './dtos/new-recipe.input';
import { RecipesArgs } from './dtos/recipes.args';
import { RecipeEntity } from './entities/recipe.entity';

@Injectable()
export class RecipesService {
  constructor(
    @InjectRepository(RecipeEntity)
    private readonly recipeRepository: Repository<RecipeEntity>
  ) {}

  async create(data: NewRecipeInput): Promise<RecipeEntity> {
    // const newData = await this.recipeRepository.save(create);
    const recipe = await this.recipeRepository.save(data);
    return recipe;
  }

  async findOneById(id: string): Promise<RecipeEntity> {
    const recipe = await this.recipeRepository.findOne({
      where: { id },
    });
    return recipe;
  }

  async findAll(recipesArgs: RecipesArgs): Promise<RecipeEntity[]> {
    const res = await this.recipeRepository.find(recipesArgs);
    return res;
  }

  async remove(id: string): Promise<boolean> {
    const recipe = await this.recipeRepository.findOne({
      where: { id },
    });

    if (!recipe) {
      throw new HttpException('Comment not found', HttpStatus.BAD_REQUEST);
    }

    // auth
    // if (recipe.author.id !== userId) {
    //   throw new HttpException(
    //     'You do not own this recipe',
    //     HttpStatus.UNAUTHORIZED
    //   );
    // }

    await this.recipeRepository.delete(recipe);

    // return recipe;
    return true;
  }
}
