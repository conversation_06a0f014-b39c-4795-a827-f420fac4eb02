import {
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  CreateDateColumn,
  Column,
} from 'typeorm';
import { Directive, Field, ID, ObjectType } from '@nestjs/graphql';

@ObjectType({ description: 'recipe对象类型' })
@Entity('recipe')
export class RecipeEntity {
  @Field((type) => ID, { description: '主键id' })
  @PrimaryGeneratedColumn()
  id: string;

  // 创建时间
  @CreateDateColumn()
  @Field({ description: '创建时间' })
  createTime: Date;

  // 更新时间
  @UpdateDateColumn()
  @Field({ description: '更新时间' })
  updateTime: Date;

  @Field({ description: '自动转大写的title' })
  @Column()
  @Directive('@upper')
  title: string;

  @Field({ nullable: true, description: '描述信息' })
  @Column()
  description?: string;

  @Field()
  @Column()
  creationDate: Date;

  @Field((type) => String)
  @Column()
  ingredients: string;
}
