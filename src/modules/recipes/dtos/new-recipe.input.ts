import { Field, InputType } from '@nestjs/graphql';
import { IsOptional, Length, MaxLength } from 'class-validator';

/**
 * create an input type
 * @link https://docs.nestjs.com/graphql/mutations#code-first
 * @link https://graphql.org/learn/schema/#input-types
 */
@InputType({ description: '新增菜单参数' })
export class NewRecipeInput {
  @Field()
  @MaxLength(30)
  title: string;

  @Field({ nullable: true })
  @IsOptional()
  @Length(10, 255)
  description?: string;

  @Field((type) => String)
  ingredients: string;

  @Field()
  creationDate: Date;
}
