import {
  Injectable,
  CallHandler,
  NestInterceptor,
  ExecutionContext,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { updateChainId, genTraceId } from 'src/utils/logger-utils';

@Injectable()
export class TraceidInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest(); // 获取request对象
    try {
      // 增加traceId和chainId
      if (!request?.headers?.traceid) {
        request.headers.traceid = genTraceId();
      }

      // 始终要更新调用链，调用增加一个节点的调用，例如 0 -> 0.0
      request.headers.chainid = updateChainId(request.headers.chainid);
    } catch (error) {}

    // 执行下一个处理程序
    return next.handle();
  }
}
