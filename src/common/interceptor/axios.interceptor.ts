import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { RequestContext } from 'nestjs-request-context';
import { updateChainId } from 'src/utils/logger-utils';

@Injectable()
export class AxiosInterceptorService implements NestInterceptor {
  constructor(private readonly httpService: HttpService) {
    // 添加请求拦截器
    this.httpService.axiosRef.interceptors.request.use((config) => {
      // 原始请求头
      const originalHeaders = this.getOriginalHeaders();
      // 在请求头中添加额外的参数
      if (originalHeaders) {
        let chainId = originalHeaders?.chainid;
        const traceId = originalHeaders?.traceid;
        try {
          const ctx: any = RequestContext.currentContext;
          if (!ctx?.requestUrls) {
            ctx.requestUrls = [];
          }
          ctx.requestUrls.push(config.url);
          if (ctx.requestUrls.length > 1) {
            // 如果调用了多个其他服务，例如调用服务a后，还调用了服务b，则调用b这次调用链需要更新chainid，chainid+1
            chainId = updateChainId(chainId, `${ctx.requestUrls.length - 1}`);
          }
        } catch (error) {}

        config.headers = {
          ...(config.headers || {}),
          chainId, // 调用链chainId
          traceId, // 调用链traceId
        };
      }
      return config;
    });
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // 将原始请求的请求头存储在请求对象中
    const httpContext = context.switchToHttp();
    const request = httpContext.getRequest();
    const ctx: any = RequestContext.currentContext;
    ctx.originalHeaders = request.headers;
    return next.handle();
  }

  private getOriginalHeaders(): Record<string, string> {
    const ctx: any = RequestContext.currentContext;
    return ctx?.originalHeaders;
  }
}
