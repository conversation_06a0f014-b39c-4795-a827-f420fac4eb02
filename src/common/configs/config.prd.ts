import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import type { Config } from './config.interface';

const config: Config = {
  nest: {
    port: 3000,
  },
  cors: {
    enabled: true,
  },
  // 数据库配置
  mysql: {
    type: 'mysql',
    host: 'xxx.xxx.xxx.xxx',
    port: 3306,
    username: 'xxx',
    password: 'xxx',
    database: 'xxx',
    autoLoadEntities: true,
    // 扫描本项目中.entity.ts或者.entity.js的文件
    entities: [`${__dirname}/**/*.entity{.ts,.js}`],
    synchronize: false,
    namingStrategy: new SnakeNamingStrategy(),
  },
  swagger: {
    enabled: true,
    title: 'Nestjs FTW',
    description: 'The nestjs API description',
    version: '1.5',
    path: 'api/docs',
  },
  apolloConfig: {
    appId: 'nextjs-test.c0279a025158c28d9c55d652ca68effe',
    cluster: 'default',
    namespaces: ['application'],
    initialConfigs: {},
    listenOnNotification: true,
    fetchCacheInterval: 5 * 60e3,
  },
  nodeTraceConfig: {
    appName: 'web-assistant',
    env: 'prod',
  },
};

export default config;
