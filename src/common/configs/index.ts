import prdConfig from './config.prd';
import sitConfig from './config.sit';
import devConfig from './config.dev';
import localConfig from './config.local';
import { getWinstonConfig } from './winston.config';
import { Config, Region } from './config.interface';

export const regionConfig: { [key in Region]: any } = {
  [Region.DEV]: devConfig,
  [Region.SIT]: sitConfig,
  [Region.PRD]: prdConfig,
  [Region.Local]: localConfig,
};

const config: Config =
  regionConfig[(process.env.REGION || Region.Local) as Region];

export default (): Config => config;

export const winstonConfig = getWinstonConfig({
  env: process.env.REGION === 'prd' ? 'prod' : 'dev',
});
