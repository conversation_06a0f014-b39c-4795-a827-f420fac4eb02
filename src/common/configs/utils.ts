import { Region, Area } from './config.interface';
import { apolloConfigHostMap } from './constants';

/**
 * 根据环境获取apollo配置中心的注册地址
 */
export function getApolloConfigServiceUrl() {
  const region = (process.env.REGION || Region.DEV) as Region;
  const area = process.env.AREA as Area;
  const apolloConfigEnv = [region, area].filter((it) => !!it).join('_');
  const configServerUrl = apolloConfigHostMap[apolloConfigEnv];
  if (!configServerUrl) {
    throw new Error(
      `[ApolloConfigService]: 无效的apollo服务器地址，请检查启动脚本中'REGION'环境(${region})和'AREA'集群(${area})配置 => ${apolloConfigEnv}，有效配置值：${Object.keys(
        apolloConfigHostMap
      ).join(',')}`
    );
  }
  return configServerUrl;
}
