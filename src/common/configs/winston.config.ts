import * as winston from 'winston';
import * as HtOctopusTransport from '@ht/winston-transport-octopus';

// TODO 提取配置
export const getWinstonConfig = ({ env = 'prod', level = 'info' }) => {
  console.log('winston config env:', env);
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  // const HtOctopusTransport = require('@ht/winston-transport-octopus');
  console.log(HtOctopusTransport);
  const transport = [
    new HtOctopusTransport({
      env,
      appId: '001407',
      serviceName: 'web-assistant',
    }),
    new winston.transports.Console(),
  ];

  return {
    level,
    format: winston.format.combine(
      winston.format.colorize({
        colors: {
          info: 'blue',
          warn: 'yellow',
          debug: 'blue',
        },
      }),
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      winston.format.printf((info) => {
        if (info.stack) {
          info.message = `message: ${info.message} \n stack: ${JSON.stringify(
            info.stack
          )}`;
        }
        return `${info.timestamp} [${info.pid}] ${info.level}: [${
          info.context || 'Application'
        }] ${info.message}`;
      })
    ),
    defaultMeta: { pid: process.pid },
    transports: transport,
  };
};
