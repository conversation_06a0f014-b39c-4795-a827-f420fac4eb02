import { ClusterClientOptions } from '@liaoliaots/nestjs-redis';
import { ApolloDriverConfig } from '@nestjs/apollo';
import { BaseDataSourceOptions } from 'typeorm/data-source/BaseDataSourceOptions';
/** apollo-config */
import { CtripApolloClientConfig } from 'nestjs-ctrip-apollo-client';

export interface MysqlConfig extends BaseDataSourceOptions {
  // type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  autoLoadEntities: boolean;
  entities: any;
  synchronize: boolean;
}

export interface NestConfig {
  port: number;
}

export interface CorsConfig {
  enabled: boolean;
}

export interface SwaggerConfig {
  enabled: boolean;
  title: string;
  description: string;
  version: string;
  path: string;
}

/**
 * 环境
 */
export enum Region {
  Local = 'local',
  DEV = 'dev',
  SIT = 'sit',
  PRD = 'prd',
}

/**
 * 集群
 */
export enum Area {
  /**
   * 奥体
   */
  AT = 'aoti',
  /**
   * 吉山
   */
  JS = 'jishan',
}

export interface NodeTraceConfig {
  appName: string;
  env: 'prod' | 'dev';
}

export interface Config {
  mysql?: MysqlConfig;
  redis?: ClusterClientOptions;
  nest: NestConfig;
  cors: CorsConfig;
  swagger: SwaggerConfig;
  graphql?: ApolloDriverConfig & {
    maxComplexity?: number;
  };
  apolloConfig?: Omit<CtripApolloClientConfig, 'configServerUrl'>;
  nodeTraceConfig: NodeTraceConfig;
}
