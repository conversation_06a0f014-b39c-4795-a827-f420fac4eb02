/**
 * apollo配置中心 - 服务注册地址
 * 其中，key为 REGION_AREA 或 REGION( REGION 和 AREA 分别为 运行环境 和 集群 环境变量值 )
 * @link http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=35176107
 */
export const apolloConfigHostMap = {
  dev: 'http://***********:8085',
  // http://************:8080,http://************:8080
  // uat: 'http://************:8080',
  // http://*************:8080,http://*************:8080,http://*************:8080
  sit: 'http://*************:8080',
  /**
   * 奥体
   * http://***********:8080,http://***********:8080,
   * 吉山
   * http://*************:8080,http://*************:8080
   */
  prd_aoti: 'http://***********:8080',
  prd_jishan: 'http://*************:8080',
};
