import { CustomScalar, Scalar } from '@nestjs/graphql';
import { Kind, ValueNode } from 'graphql';

/**
 * custom scalar types
 * @link https://docs.nestjs.com/graphql/scalars#override-a-default-scalar
 */
@Scalar('Date', (type) => Date)
export class DateScalar implements CustomScalar<number, Date> {
  description = '注意: Date custom scalar type';

  parseValue(value: number): Date {
    return new Date(value); // value from the client
  }

  serialize(value: Date): number {
    return value.getTime(); // value sent to the client
  }

  parseLiteral(ast: ValueNode): Date {
    if (ast.kind === Kind.INT) {
      return new Date(ast.value);
    }
    return null;
  }
}
