import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Logger } from 'winston';
import { Response } from 'express';
import { GqlArgumentsHost, GqlExceptionFilter } from '@nestjs/graphql';
import { GraphQLResolveInfo } from 'graphql';

@Catch(HttpException)
export class HttpExceptionFilter
  implements ExceptionFilter, GqlExceptionFilter
{
  constructor(private readonly logger: Logger) {}

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    const gqlHost = GqlArgumentsHost.create(host);
    const info = gqlHost.getInfo<GraphQLResolveInfo>();

    const status = exception.getStatus
      ? exception.getStatus()
      : HttpStatus.INTERNAL_SERVER_ERROR;

    if (status === HttpStatus.INTERNAL_SERVER_ERROR) {
      // tslint:disable-next-line: no-console
      console.error('[HttpExceptionFilter]:', exception);
    }

    const errorResponse = {
      statusCode: status,
      timestamp: new Date().toLocaleString(),
      error:
        status !== HttpStatus.INTERNAL_SERVER_ERROR
          ? (exception.message as any).error ||
            // This is for GRAPHQL petitions
            (exception as any)?.response?.message?.join?.(',') ||
            exception.message ||
            null
          : 'Internal server error',
    };

    // This is for REST petitions
    if (request) {
      const error = {
        ...errorResponse,
        path: request.url,
        method: request.method,
      };

      const { message } = exception;
      const content = `${request.method} -> ${request.url}`;
      this.logger.error(
        `[HttpExceptionFilter] ${content} - ${JSON.stringify(error)}`
      );

      const exceptionResponse: any = exception.getResponse();
      let validatorMessage = exceptionResponse;
      if (typeof validatorMessage === 'object') {
        validatorMessage = exceptionResponse?.message;
        // validatorMessage = exceptionResponse.message[0];
      }

      response.status(HttpStatus.OK).json({
        code: status,
        message: validatorMessage || message,
        data: null,
        path: request ? request.url : null,
        timestamp: new Date().toISOString(),
      });
    } else {
      // This is for GRAPHQL petitions
      const error = {
        ...errorResponse,
        type: info.parentType,
        field: info.fieldName,
      };

      const content = `${info.parentType} -> ${info.fieldName}`;

      this.logger.error(
        `[HttpExceptionFilter GQL] ${content} - ${JSON.stringify(error)}`
      );

      return exception;
    }
  }
}
