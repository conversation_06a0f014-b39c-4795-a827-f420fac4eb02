import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { NestExpressApplication } from '@nestjs/platform-express';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import * as bodyParser from 'body-parser';
import { join } from 'path';

import { HttpExceptionFilter } from 'src/common/filter/http-exception.filter';
import { TraceidInterceptor } from 'src/common/interceptor/traceid.interceptor';
import { LoggingInterceptor } from 'src/common/interceptor/logging.interceptor';
import { Tracer } from 'src/utils/node-tracer';

import type {
  CorsConfig,
  NestConfig,
  SwaggerConfig,
  NodeTraceConfig,
} from 'src/common/configs/config.interface';
import { AppModule } from './app.module';

async function bootstrap() {
  const prefix = 'web-assistant';

  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // 全局Logger
  const nestWinston = app.get(WINSTON_MODULE_NEST_PROVIDER);
  app.useLogger(nestWinston);

  app.setGlobalPrefix(prefix);
  // Validation
  app.useGlobalPipes(new ValidationPipe());
  app.useGlobalFilters(new HttpExceptionFilter(nestWinston.logger));

  app.use(bodyParser.json({ limit: '500mb' }));
  app.use(bodyParser.urlencoded({ limit: '10mb', extended: true }));
  // 设置静态资源
  app.useStaticAssets(join(__dirname, '..', 'static'), {
    prefix: `/${prefix}/static`,
  });
  app.setBaseViewsDir(join(__dirname, '..', 'views')); // 放视图的文件
  app.setViewEngine('ejs');

  const configService = app.get(ConfigService);
  const nestConfig = configService.get<NestConfig>('nest');
  const corsConfig = configService.get<CorsConfig>('cors');
  const swaggerConfig = configService.get<SwaggerConfig>('swagger');

  app.useGlobalInterceptors(new TraceidInterceptor()); // traceid全局拦截器

  const nodeTraceConfig = configService.get<NodeTraceConfig>('nodeTraceConfig');

  // 全局Tracer
  const tracer = new Tracer({
    appName: nodeTraceConfig.appName,
    env: nodeTraceConfig.env,
    initial: false,
  });

  app.useGlobalInterceptors(new LoggingInterceptor(nestWinston.logger, tracer));

  // Swagger Api
  if (swaggerConfig.enabled) {
    const options = new DocumentBuilder()
      .setTitle(swaggerConfig.title || 'Nestjs')
      .setDescription(swaggerConfig.description || 'The nestjs API description')
      .setVersion(swaggerConfig.version || '1.0')
      .build();
    const document = SwaggerModule.createDocument(app, options);

    SwaggerModule.setup(swaggerConfig.path || 'api', app, document);
  }

  // Cors
  if (corsConfig.enabled) {
    app.enableCors();
  }

  await app.listen(process.env.PORT || nestConfig.port || 3000);
}
bootstrap();
