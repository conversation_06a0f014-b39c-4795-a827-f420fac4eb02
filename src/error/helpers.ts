import { HttpException, HttpStatus } from '@nestjs/common';

export function required<T>(value: T, ...keys: (keyof T)[]) {
  if (!value) {
    throw new HttpException(
      `Keys [${keys.join(',')}] is required in value, but value is ${value}`,
      HttpStatus.BAD_REQUEST
    );
  }
  for (const key of keys) {
    if (value[key] == null) {
      throw new HttpException(
        `${String(key)} is required, but got ${value[key]}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }
}

export const mapInternalError = (message: string) => (e: Error) => {
  throw createInternalError(message)(e);
};

export const createInternalError =
  (message: string) => (e: Error | Response) => {
    return new HttpException(
      `${message}: ${e instanceof Error ? e.message : e.statusText}`,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  };
