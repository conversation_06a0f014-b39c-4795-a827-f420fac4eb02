import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { <%= classify(name) %>Service } from './<%= dasherize(name) %>.service';
import { <%= classify(name) %>Controller } from './<%= dasherize(name) %>.controller';

import { Demo<%= classify(name) %> } from './entities/index.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Demo<%= classify(name) %>])],
  controllers: [<%= classify(name) %>Controller],
  providers: [<%= classify(name) %>Service],
  exports: [],
})
export class <%= classify(name) %>Module {}
