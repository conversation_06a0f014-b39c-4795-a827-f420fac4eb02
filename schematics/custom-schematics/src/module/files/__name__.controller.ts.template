import {
  Controller,
  Post,
  Get,
  Query,
  Body,
  Headers,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { Res } from 'src/common/dto/res.dto';
import { AuthInterceptor } from 'src/common/interceptor/auth.interceptor';

import { <%= classify(name) %>Service } from './<%= dasherize(name) %>.service';

import { Demo<%= classify(name) %>Dto } from './dtos/index.dto';

@Controller('/v1/<%= name %>')
export class <%= classify(name) %>Controller {
  constructor(private readonly <%= camelize(name) %>Service: <%= classify(name) %>Service) {}

  @Post('/demo')
  @UseInterceptors(AuthInterceptor) // 使用拦截器进用户身份鉴权
  async demo(@Body() payload: Demo<%= classify(name) %>Dto, @Headers('iv-user') ivUser) {
    try {
      const { name } = payload;
      const res = await this.<%= camelize(name) %>Service.demo(name, ivUser);
      return new Res().success(res);
    } catch (error) {
      return new Res().error(error.code, error.message);
    }
  }
}
