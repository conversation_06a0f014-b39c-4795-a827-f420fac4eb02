import { Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

import { Demo<%= classify(name) %> } from './entities/index.entity';

@Injectable()
export class <%= classify(name) %>Service {
  constructor(
    @InjectRepository(Demo<%= classify(name) %>)
    private readonly demo<%= classify(name) %>Repository: Repository<Demo<%= classify(name) %>>,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger
  ) {}

  // demo 方法
  async demo(name: string, userId: string) {
    // await this.demoMyModule5Repository.findOne({
    //   where: { name },
    // });
    this.logger.info(`demo: ${name} ${userId}`);
    return `demo: ${name} ${userId}`;
  }
}
