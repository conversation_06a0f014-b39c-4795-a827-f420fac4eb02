import { Rule, SchematicContext, Tree, url, apply, template, move, mergeWith, chain } from '@angular-devkit/schematics';
import { strings } from '@angular-devkit/core';
import * as path from 'path';

// 自定义Rule来重命名文件
function renameFilesInDir(directoryPath: string): Rule {
  return (tree: Tree, _context: SchematicContext) => {
    tree.getDir(directoryPath).visit(filePath => {
      if (path.extname(filePath) === '.template') {
        const newFilePath = filePath.replace(/\.template$/, '');
        const content = tree.read(filePath);
        if (content) {
          tree.create(newFilePath, content.toString());
          tree.delete(filePath);
        }
      }
    });
    return tree;
  };
}

export function customModule(_options: any): Rule {
  const modulePath = _options.path || 'src/modules' + '/' + _options.name;
  return (tree: Tree, _context: SchematicContext) => {
    const sourceTemplates = url('./files');
    const sourceParametrizedTemplates = apply(sourceTemplates, [
      template({
        ..._options,
        ...strings,
      }),
      move(modulePath),
    ]);

    // 应用模板并重命名文件
    // 使用mergeWith来合并模板应用，并创建重命名规则
    const templateRule = mergeWith(sourceParametrizedTemplates);
    const renameRule = renameFilesInDir(modulePath);

    return chain([templateRule, renameRule])(tree, _context);  };
}
