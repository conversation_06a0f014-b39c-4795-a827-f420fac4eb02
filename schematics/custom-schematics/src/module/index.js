"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.customModule = void 0;
const schematics_1 = require("@angular-devkit/schematics");
const core_1 = require("@angular-devkit/core");
const path = require("path");
// 自定义Rule来重命名文件
function renameFilesInDir(directoryPath) {
    return (tree, _context) => {
        tree.getDir(directoryPath).visit(filePath => {
            if (path.extname(filePath) === '.template') {
                const newFilePath = filePath.replace(/\.template$/, '');
                const content = tree.read(filePath);
                if (content) {
                    tree.create(newFilePath, content.toString());
                    tree.delete(filePath);
                }
            }
        });
        return tree;
    };
}
function customModule(_options) {
    const modulePath = _options.path || 'src/modules' + '/' + _options.name;
    return (tree, _context) => {
        const sourceTemplates = (0, schematics_1.url)('./files');
        const sourceParametrizedTemplates = (0, schematics_1.apply)(sourceTemplates, [
            (0, schematics_1.template)(Object.assign(Object.assign({}, _options), core_1.strings)),
            (0, schematics_1.move)(modulePath),
        ]);
        // 应用模板并重命名文件
        // 使用mergeWith来合并模板应用，并创建重命名规则
        const templateRule = (0, schematics_1.mergeWith)(sourceParametrizedTemplates);
        const renameRule = renameFilesInDir(modulePath);
        return (0, schematics_1.chain)([templateRule, renameRule])(tree, _context);
    };
}
exports.customModule = customModule;
//# sourceMappingURL=index.js.map