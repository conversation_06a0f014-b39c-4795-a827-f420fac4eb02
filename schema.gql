# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

directive @upper on FIELD_DEFINITION

"""注意: Date custom scalar type"""
scalar Date

type Mutation {
  """新增菜单"""
  addRecipe(newRecipeData: NewRecipeInput!): RecipeEntity!

  """删除菜单"""
  removeRecipe(id: String!): Boolean!
}

"""新增菜单参数"""
input NewRecipeInput {
  creationDate: Date!
  description: String
  ingredients: String!
  title: String!
}

type Query {
  """菜单详情"""
  recipe(id: String!): RecipeEntity!

  """菜单列表"""
  recipes(skip: Int! = 0, take: Int! = 25): [RecipeEntity!]!
}

"""recipe对象类型"""
type RecipeEntity {
  """创建时间"""
  createTime: Date!
  creationDate: Date!

  """描述信息"""
  description: String

  """主键id"""
  id: ID!
  ingredients: String!

  """自动转大写的title"""
  title: String!

  """更新时间"""
  updateTime: Date!
}

type Subscription {
  """订阅新增"""
  recipeAdded: RecipeEntity!
}