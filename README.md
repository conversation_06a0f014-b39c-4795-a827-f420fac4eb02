## 模板简介

基于nestjs框架的服务端开发模板，提供以下能力：

-   支持多环境配置
-   支持mysql数据库连接
-   支持接入服务端APM
-   支持镜像构建、部署
-   支持API服务
-   支持ejs模板
-   支持服务端心跳

## 快速上手

### 本地开发

```bash
# 安装依赖
yarn 

# 
yarn dev

```

### 环境配置

配置文件目录见 src/common/configs，默认提供 local（本地开发），dev，sit，prd环境配置，通过执行yarn start:xxx命令执行相应的配置，部署时环境变量可以通过CD流水线传入；

用户可以编写src/common/configs/index中代码添加其他环境以满足实际需要

### Mysql数据库配置

可通过多种方式进行配置
#### 方式1. 文件配置(默认)

使用前用户需要在 src/common/configs/config.{env}.js中添加mysql配置以连接数据库：
```
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

{
    mysql: {
        type: 'mysql',
        host: 'xxx.xxx.xxx.xxx',   // 数据库地址
        port: 3306,                // 端口
        username: 'xxx',           // 用户名
        password: 'xxx',           // 密码
        database: 'xxx',           // 数据库名称
        namingStrategy: new SnakeNamingStrategy(),
    }
}

```
注：通过`SnakeNamingStrategy`处理命名问题，如：
```
// config.{env}.js配置
{
    mysql: {
        namingStrategy: new SnakeNamingStrategy(),
    }
}

// xxx.entity.ts
@Entity()
class User {
  @Column()
  createdAt;
}
```
createdAt 对应 DB 中 created_at 字段(默认为createdAt)。

#### 方式2. 从apollo配置中心读取数据库配置

1. 修改app.module.ts
   ```
    // TypeOrmModule.forRoot(config().mysql as TypeOrmModuleOptions),
    TypeOrmModule.forRootAsync({
      useFactory: async (
        ctripApolloClientService: CtripApolloClientService
      ) => {
        const mysqlApolloConfig = await ctripApolloClientService.fetchConfigs({
          keys: ['username', 'password'],
        });
        return {
          ...config().mysql,
          ...mysqlApolloConfig,
        } as TypeOrmModuleOptions;
      },
      inject: [CtripApolloClientService],
    }),
   ```
2. 


### APM配置
使用前用户需要在 src/common/configs/winston.config.js中添加apm配置：
```
new HtOctopusTransport({
    env,
    appId: 'xxx',               // 指南针应用ID
    serviceName: 'xxx',         // 服务名称
}),
```

### apollo配置
使用前用户需要在 src/common/configs/config.{env}.js中添加apolloConfig配置以连接apollo配置中心：
1. [apollo测试环境配置地址](http://***********:8070/apollo/) -> 创建项目 -> 修改配置
2. [apollo wiki文档](http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=34583726)
3. [apollo生产环境配置地址](http://eip.htsc.com.cn/apollo) -> 创建项目 -> 修改配置
```
{
    appId: 'nextjs-test.c0279a025158c28d9c55d652ca68effe',
    cluster: 'default',
    namespaces: ['application'],
    initialConfigs: {},
    listenOnNotification: true,
    fetchCacheInterval: 5 * 60e3,
}
```

### 镜像构建、部署
提供默认的Dockerfile文件docker/Dockerfile满足绝大多数构建部署场景；

注意Dockerfile中启动命令，env 参数为环境变量，可以赋值为dev、sit、prd，env参数需要从CD流水线传入
```
ENTRYPOINT sh /app/start.sh ${env}
```

### API开发
完成初始配置后，可以进行API开发，开发实例见src/modules/demo，demo示例为get接口，可以通过 http://localhost:3000/web-assistant/api/v1/demo/ht 访问

### ejs模板

提供ejs模板支持，demo见 src/modules/demo-view，可以通过http://localhost:3000/web-assistant/demo-view/ht 访问

### graphql

提供graphql支持，demo见 src/modules/recipes，访问playground: http://localhost:3000/web-assistant/graphql

注：实际访问路径请查看对应环境下的配置项`graphql.path`

### 示例包含内容：
1. 自定义directive -> `src/common/directives/upper-case.directive.ts`
2. 自定义scalar -> `src/common/scalars/date.scalar.ts`
3. Query + Mutation + Subscription => `src/modules/recipes` + `src/schema.gql`

#### 访问gql示例：
注：将query和variables分别写入上面的playground中进行体验
1. 查询 query:
   ```gql
   query Recipes($skip: Int, $take:Int) {
     recipes(skip: $skip, take: $take) {
       creationDate
       description
       ingredients
       id
       title
       createTime
       updateTime
     }
   }
   ```
   variables:
   ```json
   {
     "skip": 0,
     "take": 10
   }
   ```

2. 添加 mutaion:
   ```gql
   mutation addRecipe($newRecipeData: NewRecipeInput!) {
     addRecipe(newRecipeData: $newRecipeData) {
       id
       title
     }
   }
   ```
   variables:
   ```json
   {
     "newRecipeData": {
       "description": "the first recipe",
       "ingredients": "a,b,c",
       "title": "abc",
       "creationDate": 1687146264397
     }
   }
   ```

3. 订阅 subscription:
   注：开启订阅，然后添加一条菜单来测试
   ```gql
      subscription recipeAdded {
        recipeAdded {
          title
          id
          description
        }
      }
   ```

### 心跳

默认提供心跳接口 http://localhost:3000/web-assistant/heartbeat/ping，返回'pong'

## 目录结构

|---docker                      // dockerFile文件目录
|---sql                         // sql文件目录，生产上线需要通过应用空间-devops-数据流水线上线
|---src                         // 代码
    |---common                  // 通用模块
        |---configs             // 配置
        |---dto               
        |---filter             
        |---interceptor         
        |---middleware
        |---model
    |---error                   // 异常
    |---modules                 // 业务模块
    |---utils                   // 工具库
    |---app.controller.ts       // 默认controller
    |---app.module.ts           // 默认module
    |---main.ts                 // 入口文件
|---static                      // 静态资源，ejs模板中会使用
|---views                       // ejs模板
|---start.sh                    // 部署环境启动脚本